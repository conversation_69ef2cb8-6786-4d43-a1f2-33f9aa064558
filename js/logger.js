// Enhanced Error Handling and Logging System
class GameLogger {
    constructor() {
        this.logs = [];
        this.errors = [];
        this.debugMode = true;
        this.maxLogs = 50;
        this.maxErrors = 20;
        
        // Performance monitoring
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;
        
        this.setupErrorHandling();
    }
    
    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (event) => {
            this.logError('Global Error', event.error || event.message, event.filename, event.lineno);
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Unhandled Promise Rejection', event.reason);
        });
    }
    
    logDebug(category, message) {
        if (!this.debugMode) return;
        
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${category}: ${message}`;
        
        this.logs.push(logEntry);
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        
        this.updateDebugPanel();
    }
    
    logError(category, message, file = '', line = '') {
        const timestamp = new Date().toLocaleTimeString();
        const errorEntry = {
            timestamp,
            category,
            message: message.toString(),
            file,
            line,
            stack: new Error().stack
        };
        
        this.errors.push(errorEntry);
        if (this.errors.length > this.maxErrors) {
            this.errors.shift();
        }
        
        this.updateErrorPanel();
    }
    
    updateDebugPanel() {
        const debugLog = document.getElementById('debugLog');
        if (debugLog) {
            debugLog.innerHTML = this.logs.slice(-10).map(log => 
                `<div class="debug-item">${log}</div>`
            ).join('');
            debugLog.scrollTop = debugLog.scrollHeight;
        }
    }
    
    updateErrorPanel() {
        const errorLog = document.getElementById('errorLog');
        const errorLogContent = document.getElementById('errorLogContent');
        
        if (errorLogContent) {
            errorLogContent.innerHTML = this.errors.slice(-5).map(error => 
                `<div class="error-item">[${error.timestamp}] ${error.category}: ${error.message}</div>`
            ).join('');
            
            if (this.errors.length > 0) {
                errorLog.style.display = 'block';
            }
        }
    }
    
    updateFPS() {
        this.frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - this.lastTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - lastTime));
            this.frameCount = 0;
            this.lastTime = currentTime;
            
            const fpsElement = document.getElementById('fps');
            if (fpsElement) {
                fpsElement.textContent = this.fps;
            }
        }
    }
    
    showAchievement(text, icon = '🏆') {
        const notification = document.getElementById('achievementNotification');
        if (notification) {
            const iconElement = notification.querySelector('.achievement-icon');
            const textElement = notification.querySelector('.achievement-text');
            
            if (iconElement && textElement) {
                iconElement.textContent = icon;
                textElement.textContent = text;
                notification.style.display = 'block';
                
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 3000);
                
                this.logDebug('Achievement', text);
            }
        }
    }
}

// Safe function wrapper
function safeExecute(fn, context = 'Unknown') {
    try {
        return fn();
    } catch (error) {
        if (window.logger) {
            window.logger.logError(context, error.message);
        }
        return null;
    }
}

// Initialize logger when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.logger = new GameLogger();
    window.logger.logDebug('System', 'Logger initialized successfully');
});
