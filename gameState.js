// Game State Management - Texas Pond Fishing Simulator

class GameState {
    constructor() {
        this.mode = 'stocking';
        this.budget = 5000;
        this.ph = 7.2;
        this.temperature = 72;
        this.oxygen = 8.5;
        this.grassHealth = 85;
        this.fishPopulation = {
            bluegill: 0,
            minnows: 0,
            shad: 0,
            bass: 0,
            catfish: 0,
            pike: 0
        };
        this.plants = 0;
        this.hasDock = false;
        this.fishingState = {
            casting: false,
            reeling: false,
            fishHooked: false,
            lineTension: 0,
            castPower: 50,
            fishFighting: false,
            currentFish: null,
            lineInWater: false,
            depth: 0
        };
        this.weather = {
            type: 'sunny',
            wind: 5,
            pressure: 30.12,
            temperature: 75
        };
        this.playerPosition = { x: 0, y: 0, z: 5 };
        this.isInitialized = false;
        this.achievements = [];
        this.totalFishCaught = 0;
        this.largestFish = 0;
        this.timeOfDay = 'morning';
        this.season = 'spring';
        
        // Initialize weather system
        this.initializeWeather();
        
        // Start ecosystem simulation
        this.startEcosystemSimulation();
    }
    
    // Validate game state
    validate() {
        const errors = [];
        
        if (this.budget < 0) {
            errors.push('Budget cannot be negative');
            this.budget = 0;
        }
        
        if (this.ph < 6.0 || this.ph > 8.5) {
            errors.push('pH out of range');
            this.ph = Math.max(6.0, Math.min(8.5, this.ph));
        }
        
        if (this.temperature < 32 || this.temperature > 100) {
            errors.push('Temperature out of range');
            this.temperature = Math.max(32, Math.min(100, this.temperature));
        }
        
        if (this.oxygen < 0 || this.oxygen > 15) {
            errors.push('Oxygen out of range');
            this.oxygen = Math.max(0, Math.min(15, this.oxygen));
        }
        
        if (errors.length > 0 && window.logger) {
            window.logger.logError('Game State Validation', errors.join(', '));
        }
        
        return errors.length === 0;
    }
    
    // Update UI elements
    updateUI() {
        safeExecute(() => {
            // Update basic stats
            const modeEl = document.getElementById('mode');
            const budgetEl = document.getElementById('budget');
            const phEl = document.getElementById('ph');
            const tempEl = document.getElementById('temp');
            const oxygenEl = document.getElementById('oxygen');
            const grassEl = document.getElementById('grass');
            const fishCountEl = document.getElementById('fishCount');
            const balanceEl = document.getElementById('balance');
            
            if (modeEl) modeEl.textContent = this.mode.charAt(0).toUpperCase() + this.mode.slice(1);
            if (budgetEl) budgetEl.textContent = `$${this.budget}`;
            if (phEl) phEl.textContent = this.ph.toFixed(1);
            if (tempEl) tempEl.textContent = `${this.temperature}°F`;
            if (oxygenEl) oxygenEl.textContent = `${this.oxygen.toFixed(1)} ppm`;
            if (grassEl) grassEl.textContent = `${this.grassHealth}%`;
            
            // Calculate total fish count
            const totalFish = Object.values(this.fishPopulation).reduce((sum, count) => sum + count, 0);
            if (fishCountEl) fishCountEl.textContent = totalFish;
            
            // Calculate ecosystem balance
            const balance = this.calculateEcosystemBalance();
            if (balanceEl) balanceEl.textContent = `${balance}%`;
            
            // Update weather
            const weatherTypeEl = document.getElementById('weatherType');
            const windEl = document.getElementById('wind');
            const pressureEl = document.getElementById('pressure');
            
            if (weatherTypeEl) weatherTypeEl.textContent = this.weather.type;
            if (windEl) windEl.textContent = `${this.weather.wind} mph SW`;
            if (pressureEl) pressureEl.textContent = `${this.weather.pressure} in`;
            
            // Update mode toggle button
            const modeToggleText = document.getElementById('modeToggleText');
            if (modeToggleText) {
                modeToggleText.textContent = this.mode === 'stocking' ? '🎣 Switch to Fishing' : '🐟 Switch to Stocking';
            }
            
            // Update fishing UI if in fishing mode
            if (this.mode === 'fishing') {
                this.updateFishingUI();
            }
            
        }, 'UI Update');
    }
    
    updateFishingUI() {
        safeExecute(() => {
            const castPowerElement = document.getElementById('castPower');
            const tensionTextElement = document.getElementById('tensionText');
            const fishStatusElement = document.getElementById('fishStatus');
            const dragSettingElement = document.getElementById('dragSetting');
            
            if (castPowerElement) {
                castPowerElement.textContent = `${this.fishingState.castPower}%`;
            }
            
            if (tensionTextElement) {
                tensionTextElement.textContent = `${this.fishingState.lineTension}%`;
            }
            
            if (fishStatusElement) {
                let status = 'Ready to cast';
                if (this.fishingState.casting) status = 'Casting...';
                else if (this.fishingState.lineInWater) status = 'Line in water';
                else if (this.fishingState.fishHooked) status = 'Fish hooked!';
                else if (this.fishingState.reeling) status = 'Reeling in...';
                
                fishStatusElement.textContent = status;
            }
            
            if (dragSettingElement) {
                dragSettingElement.textContent = 'Medium'; // Could be dynamic
            }
            
            // Update tension bar
            const tensionFill = document.getElementById('tensionFill');
            if (tensionFill) {
                tensionFill.style.width = `${this.fishingState.lineTension}%`;
            }
            
            // Update power bar
            const powerFill = document.getElementById('powerFill');
            if (powerFill) {
                powerFill.style.width = `${this.fishingState.castPower}%`;
            }
            
        }, 'Fishing UI Update');
    }
    
    // Calculate ecosystem balance based on fish populations and environment
    calculateEcosystemBalance() {
        const totalFish = Object.values(this.fishPopulation).reduce((sum, count) => sum + count, 0);
        
        if (totalFish === 0) return 50; // Neutral when no fish
        
        // Calculate predator to prey ratio
        const predators = this.fishPopulation.bass + this.fishPopulation.catfish + this.fishPopulation.pike;
        const prey = this.fishPopulation.bluegill + this.fishPopulation.minnows + this.fishPopulation.shad;
        
        let balance = 50; // Start neutral
        
        // Ideal ratio is about 1:4 predator to prey
        const idealRatio = 0.25;
        const currentRatio = predators / (prey + 1); // +1 to avoid division by zero
        
        // Adjust balance based on ratio
        if (currentRatio < idealRatio) {
            balance += (idealRatio - currentRatio) * 100; // Too many prey
        } else {
            balance -= (currentRatio - idealRatio) * 100; // Too many predators
        }
        
        // Factor in water quality
        const waterQuality = (this.ph - 6.5) / 1.5 * 50 + // pH factor
                           (this.oxygen - 5) / 10 * 50 + // Oxygen factor
                           this.grassHealth / 100 * 50; // Vegetation factor
        
        balance = (balance + waterQuality) / 2;
        
        return Math.max(0, Math.min(100, Math.round(balance)));
    }
    
    // Add fish to pond
    addFish(fishType, count = 1) {
        if (this.fishPopulation.hasOwnProperty(fishType)) {
            this.fishPopulation[fishType] += count;
            
            // Trigger achievement checks
            this.checkAchievements();
            
            if (window.logger) {
                window.logger.logDebug('Stocking', `Added ${count} ${fishType} to pond`);
            }
            
            return true;
        }
        return false;
    }
    
    // Remove fish from pond (when caught)
    removeFish(fishType, count = 1) {
        if (this.fishPopulation.hasOwnProperty(fishType) && this.fishPopulation[fishType] >= count) {
            this.fishPopulation[fishType] -= count;
            this.totalFishCaught += count;
            
            if (window.logger) {
                window.logger.logDebug('Fishing', `Caught ${count} ${fishType}`);
            }
            
            return true;
        }
        return false;
    }
    
    // Spend budget
    spendMoney(amount) {
        if (this.budget >= amount) {
            this.budget -= amount;
            return true;
        }
        return false;
    }
    
    // Initialize weather system
    initializeWeather() {
        this.updateWeather();
        
        // Update weather every 5 minutes
        setInterval(() => {
            this.updateWeather();
        }, 300000);
    }
    
    // Update weather conditions
    updateWeather() {
        const weatherTypes = ['sunny', 'cloudy', 'overcast', 'light rain', 'rain'];
        const currentIndex = weatherTypes.indexOf(this.weather.type);
        
        // 70% chance to stay the same, 30% chance to change
        if (Math.random() < 0.3) {
            const change = Math.random() < 0.5 ? -1 : 1;
            const newIndex = Math.max(0, Math.min(weatherTypes.length - 1, currentIndex + change));
            this.weather.type = weatherTypes[newIndex];
        }
        
        // Update wind (3-15 mph)
        this.weather.wind = Math.round(3 + Math.random() * 12);
        
        // Update pressure (29.5 - 30.5 inches)
        this.weather.pressure = (29.5 + Math.random()).toFixed(2);
        
        // Update air temperature based on water temperature
        this.weather.temperature = this.temperature + Math.round((Math.random() - 0.5) * 10);
        
        if (window.logger) {
            window.logger.logDebug('Weather', `Updated to ${this.weather.type}, ${this.weather.wind} mph wind`);
        }
    }
    
    // Start ecosystem simulation
    startEcosystemSimulation() {
        setInterval(() => {
            this.simulateEcosystem();
        }, 30000); // Every 30 seconds
    }
    
    // Simulate ecosystem changes
    simulateEcosystem() {
        // Fish reproduction and death
        Object.keys(this.fishPopulation).forEach(fishType => {
            const population = this.fishPopulation[fishType];
            if (population > 0) {
                // Small chance of population change
                const change = Math.random() < 0.1 ? (Math.random() < 0.5 ? -1 : 1) : 0;
                this.fishPopulation[fishType] = Math.max(0, population + change);
            }
        });
        
        // Water quality changes
        this.ph += (Math.random() - 0.5) * 0.1;
        this.ph = Math.max(6.0, Math.min(8.5, this.ph));
        
        this.oxygen += (Math.random() - 0.5) * 0.2;
        this.oxygen = Math.max(5.0, Math.min(12.0, this.oxygen));
        
        this.grassHealth += (Math.random() - 0.5) * 2;
        this.grassHealth = Math.max(0, Math.min(100, this.grassHealth));
        
        // Validate state after changes
        this.validate();
    }
    
    // Check for achievements
    checkAchievements() {
        const totalFish = Object.values(this.fishPopulation).reduce((sum, count) => sum + count, 0);
        
        // First fish achievement
        if (totalFish >= 1 && !this.achievements.includes('first_fish')) {
            this.achievements.push('first_fish');
            if (window.logger) {
                window.logger.showAchievement('First Fish Stocked!', '🐟');
            }
        }
        
        // Ecosystem builder achievement
        if (totalFish >= 50 && !this.achievements.includes('ecosystem_builder')) {
            this.achievements.push('ecosystem_builder');
            if (window.logger) {
                window.logger.showAchievement('Ecosystem Builder!', '🌊');
            }
        }
        
        // Balanced ecosystem achievement
        const balance = this.calculateEcosystemBalance();
        if (balance >= 80 && !this.achievements.includes('balanced_ecosystem')) {
            this.achievements.push('balanced_ecosystem');
            if (window.logger) {
                window.logger.showAchievement('Balanced Ecosystem!', '⚖️');
            }
        }
    }
    
    // Switch between stocking and fishing modes
    switchMode() {
        this.mode = this.mode === 'stocking' ? 'fishing' : 'stocking';
        
        // Show/hide appropriate panels
        const stockingPanel = document.getElementById('stockingPanel');
        const fishingPanel = document.getElementById('fishingPanel');
        const stockingInstructions = document.getElementById('stockingInstructions');
        const fishingInstructions = document.getElementById('fishingInstructions');
        
        if (this.mode === 'stocking') {
            if (stockingPanel) stockingPanel.style.display = 'block';
            if (fishingPanel) fishingPanel.style.display = 'none';
            if (stockingInstructions) stockingInstructions.style.display = 'block';
            if (fishingInstructions) fishingInstructions.style.display = 'none';
        } else {
            if (stockingPanel) stockingPanel.style.display = 'none';
            if (fishingPanel) fishingPanel.style.display = 'block';
            if (stockingInstructions) stockingInstructions.style.display = 'none';
            if (fishingInstructions) fishingInstructions.style.display = 'block';
        }
        
        if (window.logger) {
            window.logger.logDebug('Mode Switch', `Switched to ${this.mode} mode`);
        }
    }
    
    // Save game state to localStorage
    save() {
        try {
            const saveData = {
                budget: this.budget,
                fishPopulation: this.fishPopulation,
                ph: this.ph,
                temperature: this.temperature,
                oxygen: this.oxygen,
                grassHealth: this.grassHealth,
                plants: this.plants,
                hasDock: this.hasDock,
                achievements: this.achievements,
                totalFishCaught: this.totalFishCaught,
                largestFish: this.largestFish
            };
            
            localStorage.setItem('pondFishingSimulator', JSON.stringify(saveData));
            
            if (window.logger) {
                window.logger.logDebug('Save', 'Game state saved successfully');
            }
            
            return true;
        } catch (error) {
            if (window.logger) {
                window.logger.logError('Save', 'Failed to save game state: ' + error.message);
            }
            return false;
        }
    }
    
    // Load game state from localStorage
    load() {
        try {
            const saveData = localStorage.getItem('pondFishingSimulator');
            if (saveData) {
                const data = JSON.parse(saveData);
                
                this.budget = data.budget || 5000;
                this.fishPopulation = data.fishPopulation || this.fishPopulation;
                this.ph = data.ph || 7.2;
                this.temperature = data.temperature || 72;
                this.oxygen = data.oxygen || 8.5;
                this.grassHealth = data.grassHealth || 85;
                this.plants = data.plants || 0;
                this.hasDock = data.hasDock || false;
                this.achievements = data.achievements || [];
                this.totalFishCaught = data.totalFishCaught || 0;
                this.largestFish = data.largestFish || 0;
                
                if (window.logger) {
                    window.logger.logDebug('Load', 'Game state loaded successfully');
                }
                
                return true;
            }
        } catch (error) {
            if (window.logger) {
                window.logger.logError('Load', 'Failed to load game state: ' + error.message);
            }
        }
        return false;
    }
}

// Initialize game state
window.gameState = new GameState();
