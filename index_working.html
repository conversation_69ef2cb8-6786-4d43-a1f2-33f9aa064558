<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Texas Pond Fishing Simulator - Working Version</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            color: #ecf0f1;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            font-size: 18px;
        }

        .spinner {
            border: 4px solid #34495e;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(44, 62, 80, 0.95);
            color: #ecf0f1;
            padding: 20px;
            border-radius: 12px;
            font-size: 14px;
            z-index: 100;
            min-width: 280px;
            border: 2px solid #34495e;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .ui-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #34495e;
        }

        .ui-header h2 {
            color: #3498db;
            font-size: 18px;
            margin: 0;
        }

        .version {
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            padding: 5px 0;
        }

        .stat-label {
            font-weight: 500;
            color: #bdc3c7;
        }

        .stat-value {
            font-weight: bold;
            color: #ecf0f1;
            background: rgba(52, 152, 219, 0.2);
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }

        #modeToggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            z-index: 100;
        }

        #modeToggle:hover {
            background: linear-gradient(135deg, #2980b9, #1f618d);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        #stockingPanel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(44, 62, 80, 0.95);
            color: #ecf0f1;
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #34495e;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            max-width: 500px;
            z-index: 100;
        }

        #fishingPanel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(44, 62, 80, 0.95);
            color: #ecf0f1;
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #34495e;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            max-width: 500px;
            z-index: 100;
            display: none;
        }

        .stock-button {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            border: none;
            padding: 12px 16px;
            margin: 4px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
            box-shadow: 0 3px 10px rgba(39, 174, 96, 0.3);
        }

        .stock-button:hover {
            background: linear-gradient(135deg, #229954, #1e8449);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .stock-button:disabled {
            background: #7f8c8d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        #instructions {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(44, 62, 80, 0.95);
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 100;
            border: 2px solid #34495e;
            max-width: 300px;
            backdrop-filter: blur(10px);
        }

        #errorDisplay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(231, 76, 60, 0.95);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            z-index: 1001;
            display: none;
        }

        #debugInfo {
            position: absolute;
            top: 20px;
            right: 200px;
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 100;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div id="loadingScreen">
        <div class="spinner"></div>
        <div>Loading Texas Pond Fishing Simulator...</div>
        <div id="loadingStatus">Checking WebGL support...</div>
    </div>

    <div id="errorDisplay">
        <h3>⚠️ Error Loading Game</h3>
        <p id="errorMessage">Failed to initialize game.</p>
        <button onclick="location.reload()">Reload Game</button>
        <button onclick="showDebugInfo()">Show Debug Info</button>
    </div>
    
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <div class="ui-header">
                <h2>🎣 Texas Pond Simulator</h2>
                <div class="version">v2.1</div>
            </div>
            <div class="stat-row">
                <span class="stat-label">Mode:</span>
                <span id="mode" class="stat-value">Stocking</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">💰 Budget:</span>
                <span id="budget" class="stat-value">$5000</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">🐟 Fish Count:</span>
                <span id="fishCount" class="stat-value">0</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">🌡️ Water Temp:</span>
                <span id="temp" class="stat-value">72°F</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">⚖️ Ecosystem:</span>
                <span id="balance" class="stat-value">50%</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">📊 FPS:</span>
                <span id="fps" class="stat-value">--</span>
            </div>
        </div>

        <button id="modeToggle">🎣 Switch to Fishing</button>

        <div id="debugInfo">
            <strong>Debug Info:</strong><br>
            <div id="debugContent">Initializing...</div>
        </div>

        <div id="stockingPanel">
            <h3>🐟 Stock Your Pond</h3>
            <div style="margin-bottom: 15px;">
                <h4>🐠 Bait Fish</h4>
                <button class="stock-button" data-fish="bluegill" data-cost="50">
                    🐟 Bluegill<br><small>$50</small>
                </button>
                <button class="stock-button" data-fish="minnows" data-cost="30">
                    🐠 Minnows<br><small>$30</small>
                </button>
                <button class="stock-button" data-fish="shad" data-cost="40">
                    🐟 Shad<br><small>$40</small>
                </button>
            </div>
            <div>
                <h4>🦈 Predator Fish</h4>
                <button class="stock-button" data-fish="bass" data-cost="150">
                    🐟 Bass<br><small>$150</small>
                </button>
                <button class="stock-button" data-fish="catfish" data-cost="80">
                    🐱 Catfish<br><small>$80</small>
                </button>
                <button class="stock-button" data-fish="pike" data-cost="120">
                    🐟 Pike<br><small>$120</small>
                </button>
            </div>
        </div>

        <div id="fishingPanel">
            <h3>🎣 Fishing Controls</h3>
            <button id="castButton" style="
                background: linear-gradient(135deg, #e74c3c, #c0392b);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 18px;
                font-weight: bold;
                width: 100%;
                margin-bottom: 15px;
            ">🎯 Cast Line</button>
            <div>Cast Power: <span id="castPower">50%</span></div>
            <div>Line Tension: <span id="lineTension">0%</span></div>
            <div>Status: <span id="fishStatus">Ready to cast</span></div>
        </div>

        <div id="instructions">
            <h4>📋 Controls</h4>
            <div id="stockingInstructions">
                <strong>🐟 Stocking Mode:</strong><br>
                • Click fish buttons to stock pond<br>
                • Monitor ecosystem balance<br>
                • Build sustainable fish population
            </div>
            <div id="fishingInstructions" style="display: none;">
                <strong>🎣 Fishing Mode:</strong><br>
                • Click "Cast Line" to fish<br>
                • Wait for fish to bite<br>
                • Click rapidly to reel in
            </div>
        </div>
    </div>

    <script>
        // Global game state
        let gameState = {
            mode: 'stocking',
            budget: 5000,
            fishCount: 0,
            temperature: 72,
            balance: 50,
            isInitialized: false,
            fishPopulation: {
                bluegill: 0,
                minnows: 0,
                shad: 0,
                bass: 0,
                catfish: 0,
                pike: 0
            }
        };

        // Debug logging
        function debugLog(message) {
            console.log('[DEBUG]', message);
            const debugContent = document.getElementById('debugContent');
            if (debugContent) {
                debugContent.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
            }
        }

        function showDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
        }

        // Check WebGL support
        function checkWebGLSupport() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (!gl) {
                    throw new Error('WebGL not supported');
                }
                debugLog('WebGL support: ✓');
                return true;
            } catch (e) {
                debugLog('WebGL support: ✗ - ' + e.message);
                return false;
            }
        }

        // Load Three.js with multiple fallbacks
        function loadThreeJS() {
            return new Promise((resolve, reject) => {
                const cdnUrls = [
                    'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js',
                    'https://unpkg.com/three@0.128.0/build/three.min.js',
                    'https://cdn.jsdelivr.net/npm/three@0.128.0/build/three.min.js'
                ];

                let currentIndex = 0;

                function tryLoadScript() {
                    if (currentIndex >= cdnUrls.length) {
                        reject(new Error('All CDN sources failed to load Three.js'));
                        return;
                    }

                    const script = document.createElement('script');
                    script.src = cdnUrls[currentIndex];
                    
                    script.onload = () => {
                        if (typeof THREE !== 'undefined') {
                            debugLog('Three.js loaded from: ' + cdnUrls[currentIndex]);
                            resolve();
                        } else {
                            debugLog('Three.js loaded but THREE object not available');
                            currentIndex++;
                            tryLoadScript();
                        }
                    };
                    
                    script.onerror = () => {
                        debugLog('Failed to load from: ' + cdnUrls[currentIndex]);
                        currentIndex++;
                        tryLoadScript();
                    };
                    
                    document.head.appendChild(script);
                }

                tryLoadScript();
            });
        }

        // Initialize 3D scene
        function initializeScene() {
            try {
                debugLog('Initializing 3D scene...');

                // Scene setup
                const scene = new THREE.Scene();
                scene.fog = new THREE.Fog(0x87CEEB, 50, 200);

                // Camera setup
                const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                camera.position.set(0, 15, 30);
                camera.lookAt(0, 0, 0);

                // Renderer setup
                const canvas = document.getElementById('gameCanvas');
                const renderer = new THREE.WebGLRenderer({
                    canvas: canvas,
                    antialias: true,
                    powerPreference: 'high-performance'
                });

                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                renderer.setClearColor(0x87CEEB);

                // Lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(50, 100, 50);
                directionalLight.castShadow = true;
                scene.add(directionalLight);

                // Create terrain
                const terrainGeometry = new THREE.PlaneGeometry(200, 200, 50, 50);
                const terrainMaterial = new THREE.MeshLambertMaterial({ color: 0x3d5c47 });
                const terrain = new THREE.Mesh(terrainGeometry, terrainMaterial);
                terrain.rotation.x = -Math.PI / 2;
                terrain.position.y = -2;
                terrain.receiveShadow = true;
                scene.add(terrain);

                // Create pond
                const pondGeometry = new THREE.CircleGeometry(25, 64);
                const pondMaterial = new THREE.MeshLambertMaterial({
                    color: 0x1e6091,
                    transparent: true,
                    opacity: 0.8
                });
                const pond = new THREE.Mesh(pondGeometry, pondMaterial);
                pond.rotation.x = -Math.PI / 2;
                pond.position.y = -0.5;
                pond.receiveShadow = true;
                scene.add(pond);

                // Create some trees
                for (let i = 0; i < 20; i++) {
                    const treeGroup = new THREE.Group();

                    // Trunk
                    const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, 8, 8);
                    const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8b4513 });
                    const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
                    trunk.position.y = 2;
                    trunk.castShadow = true;
                    treeGroup.add(trunk);

                    // Foliage
                    const foliageGeometry = new THREE.SphereGeometry(4, 8, 6);
                    const foliageMaterial = new THREE.MeshLambertMaterial({ color: 0x228b22 });
                    const foliage = new THREE.Mesh(foliageGeometry, foliageMaterial);
                    foliage.position.y = 7;
                    foliage.castShadow = true;
                    treeGroup.add(foliage);

                    // Position randomly but not in pond
                    let x, z, attempts = 0;
                    do {
                        x = (Math.random() - 0.5) * 180;
                        z = (Math.random() - 0.5) * 180;
                        attempts++;
                    } while (Math.sqrt(x*x + z*z) < 35 && attempts < 100);

                    if (attempts < 100) {
                        treeGroup.position.set(x, -2, z);
                        scene.add(treeGroup);
                    }
                }

                // Store globally
                window.gameScene = scene;
                window.gameCamera = camera;
                window.gameRenderer = renderer;
                window.gamePond = pond;

                debugLog('3D scene initialized successfully');
                return { scene, camera, renderer };

            } catch (error) {
                debugLog('Error initializing scene: ' + error.message);
                throw error;
            }
        }

        // Game loop
        let frameCount = 0;
        let lastTime = performance.now();

        function gameLoop() {
            if (!window.gameRenderer || !window.gameScene || !window.gameCamera) {
                return;
            }

            try {
                // Update FPS counter
                frameCount++;
                const currentTime = performance.now();
                if (currentTime - lastTime >= 1000) {
                    const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                    document.getElementById('fps').textContent = fps;
                    frameCount = 0;
                    lastTime = currentTime;
                }

                // Animate pond water
                if (window.gamePond) {
                    const time = Date.now() * 0.001;
                    window.gamePond.material.opacity = 0.7 + Math.sin(time) * 0.1;
                }

                // Render scene
                window.gameRenderer.render(window.gameScene, window.gameCamera);

                requestAnimationFrame(gameLoop);
            } catch (error) {
                debugLog('Error in game loop: ' + error.message);
            }
        }

        // Handle window resize
        function onWindowResize() {
            if (window.gameCamera && window.gameRenderer) {
                window.gameCamera.aspect = window.innerWidth / window.innerHeight;
                window.gameCamera.updateProjectionMatrix();
                window.gameRenderer.setSize(window.innerWidth, window.innerHeight);
            }
        }

        // Update UI
        function updateUI() {
            document.getElementById('mode').textContent = gameState.mode.charAt(0).toUpperCase() + gameState.mode.slice(1);
            document.getElementById('budget').textContent = '$' + gameState.budget;
            document.getElementById('fishCount').textContent = gameState.fishCount;
            document.getElementById('temp').textContent = gameState.temperature + '°F';
            document.getElementById('balance').textContent = gameState.balance + '%';
        }

        // Stock fish function
        function stockFish(fishType, cost) {
            if (gameState.budget >= cost) {
                gameState.budget -= cost;
                gameState.fishPopulation[fishType] += 1;
                gameState.fishCount = Object.values(gameState.fishPopulation).reduce((sum, count) => sum + count, 0);

                // Update ecosystem balance
                const totalFish = gameState.fishCount;
                const predators = gameState.fishPopulation.bass + gameState.fishPopulation.catfish + gameState.fishPopulation.pike;
                const prey = gameState.fishPopulation.bluegill + gameState.fishPopulation.minnows + gameState.fishPopulation.shad;

                if (totalFish > 0) {
                    const ratio = predators / (prey + 1);
                    gameState.balance = Math.max(0, Math.min(100, Math.round(50 + (0.25 - ratio) * 100)));
                }

                updateUI();
                updateButtonStates();

                debugLog(`Stocked ${fishType} for $${cost}. New balance: $${gameState.budget}`);
            } else {
                debugLog(`Not enough budget for ${fishType}. Need $${cost}, have $${gameState.budget}`);
            }
        }

        // Update button states
        function updateButtonStates() {
            document.querySelectorAll('.stock-button').forEach(button => {
                const cost = parseInt(button.getAttribute('data-cost'));
                button.disabled = gameState.budget < cost;
            });
        }

        // Toggle game mode
        function toggleMode() {
            gameState.mode = gameState.mode === 'stocking' ? 'fishing' : 'stocking';

            const stockingPanel = document.getElementById('stockingPanel');
            const fishingPanel = document.getElementById('fishingPanel');
            const stockingInstructions = document.getElementById('stockingInstructions');
            const fishingInstructions = document.getElementById('fishingInstructions');
            const modeToggle = document.getElementById('modeToggle');

            if (gameState.mode === 'stocking') {
                stockingPanel.style.display = 'block';
                fishingPanel.style.display = 'none';
                stockingInstructions.style.display = 'block';
                fishingInstructions.style.display = 'none';
                modeToggle.textContent = '🎣 Switch to Fishing';
            } else {
                stockingPanel.style.display = 'none';
                fishingPanel.style.display = 'block';
                stockingInstructions.style.display = 'none';
                fishingInstructions.style.display = 'block';
                modeToggle.textContent = '🐟 Switch to Stocking';
            }

            updateUI();
            debugLog('Switched to ' + gameState.mode + ' mode');
        }

        // Setup event listeners
        function setupEventListeners() {
            // Mode toggle
            document.getElementById('modeToggle').addEventListener('click', toggleMode);

            // Stock buttons
            document.querySelectorAll('.stock-button').forEach(button => {
                button.addEventListener('click', (e) => {
                    const fishType = e.target.getAttribute('data-fish');
                    const cost = parseInt(e.target.getAttribute('data-cost'));
                    stockFish(fishType, cost);
                });
            });

            // Fishing button
            document.getElementById('castButton').addEventListener('click', () => {
                debugLog('Cast button clicked');
                // Simple fishing simulation
                setTimeout(() => {
                    if (gameState.fishCount > 0 && Math.random() < 0.7) {
                        debugLog('Fish caught!');
                        document.getElementById('fishStatus').textContent = 'Fish caught!';
                        setTimeout(() => {
                            document.getElementById('fishStatus').textContent = 'Ready to cast';
                        }, 2000);
                    } else {
                        debugLog('No fish caught');
                        document.getElementById('fishStatus').textContent = 'No bite...';
                        setTimeout(() => {
                            document.getElementById('fishStatus').textContent = 'Ready to cast';
                        }, 2000);
                    }
                }, 1000 + Math.random() * 3000);

                document.getElementById('fishStatus').textContent = 'Waiting for bite...';
            });

            // Window resize
            window.addEventListener('resize', onWindowResize);

            debugLog('Event listeners setup complete');
        }

        // Main initialization function
        async function initializeGame() {
            try {
                document.getElementById('loadingStatus').textContent = 'Checking WebGL support...';

                if (!checkWebGLSupport()) {
                    throw new Error('WebGL is not supported in your browser. Please update your browser or enable hardware acceleration.');
                }

                document.getElementById('loadingStatus').textContent = 'Loading Three.js library...';
                await loadThreeJS();

                document.getElementById('loadingStatus').textContent = 'Initializing 3D graphics...';
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for UI

                initializeScene();

                document.getElementById('loadingStatus').textContent = 'Setting up game systems...';
                setupEventListeners();
                updateUI();
                updateButtonStates();

                document.getElementById('loadingStatus').textContent = 'Starting game...';
                await new Promise(resolve => setTimeout(resolve, 500));

                // Hide loading screen and start game loop
                document.getElementById('loadingScreen').style.display = 'none';
                gameState.isInitialized = true;
                gameLoop();

                debugLog('Game initialization complete!');

            } catch (error) {
                console.error('Game initialization failed:', error);
                debugLog('Initialization failed: ' + error.message);

                document.getElementById('loadingScreen').style.display = 'none';
                document.getElementById('errorDisplay').style.display = 'block';
                document.getElementById('errorMessage').textContent = error.message;
            }
        }

        // Start the game when page loads
        document.addEventListener('DOMContentLoaded', () => {
            debugLog('DOM loaded, starting initialization...');
            initializeGame();
        });
    </script>
