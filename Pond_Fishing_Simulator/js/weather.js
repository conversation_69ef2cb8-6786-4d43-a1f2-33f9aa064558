// Weather System - Texas Pond Fishing Simulator

class WeatherSystem {
    constructor() {
        this.weatherTypes = [
            { name: 'sunny', icon: '☀️', fishingMultiplier: 0.9 },
            { name: 'partly cloudy', icon: '⛅', fishingMultiplier: 1.0 },
            { name: 'cloudy', icon: '☁️', fishingMultiplier: 1.1 },
            { name: 'overcast', icon: '🌫️', fishingMultiplier: 1.3 },
            { name: 'light rain', icon: '🌦️', fishingMultiplier: 1.5 },
            { name: 'rain', icon: '🌧️', fishingMultiplier: 0.8 },
            { name: 'thunderstorm', icon: '⛈️', fishingMultiplier: 0.3 }
        ];
        
        this.currentWeatherIndex = 0;
        this.weatherChangeTimer = 0;
        this.weatherChangeDuration = 300000; // 5 minutes in milliseconds
        
        this.windDirections = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
        this.currentWindDirection = 'SW';
        
        this.isActive = false;
        
        this.initializeWeather();
    }
    
    // Initialize weather system
    initializeWeather() {
        // Set initial weather to partly cloudy
        this.currentWeatherIndex = 1;
        this.updateWeatherDisplay();
        
        // Start weather update cycle
        this.startWeatherCycle();
    }
    
    // Start the weather update cycle
    startWeatherCycle() {
        setInterval(() => {
            this.updateWeather();
        }, this.weatherChangeDuration);
        
        // Also update wind and pressure more frequently
        setInterval(() => {
            this.updateWind();
            this.updatePressure();
        }, 60000); // Every minute
    }
    
    // Update weather conditions
    updateWeather() {
        if (!this.isActive) return;
        
        const currentWeather = this.weatherTypes[this.currentWeatherIndex];
        
        // 70% chance to stay the same, 30% chance to change
        if (Math.random() < 0.3) {
            // Determine direction of change
            const changeDirection = Math.random() < 0.5 ? -1 : 1;
            
            // Weather tends to progress in patterns
            let newIndex = this.currentWeatherIndex + changeDirection;
            
            // Keep within bounds
            newIndex = Math.max(0, Math.min(this.weatherTypes.length - 1, newIndex));
            
            // Avoid extreme weather changes (no direct sunny to thunderstorm)
            const weatherDifference = Math.abs(newIndex - this.currentWeatherIndex);
            if (weatherDifference > 2) {
                newIndex = this.currentWeatherIndex + (changeDirection > 0 ? 1 : -1);
                newIndex = Math.max(0, Math.min(this.weatherTypes.length - 1, newIndex));
            }
            
            this.currentWeatherIndex = newIndex;
            
            if (window.logger) {
                window.logger.logDebug('Weather', `Weather changed to ${this.getCurrentWeather().name}`);
            }
        }
        
        this.updateWeatherDisplay();
        this.updateGameStateWeather();
        this.checkWeatherEffects();
    }
    
    // Update wind conditions
    updateWind() {
        // Wind speed varies between 0-20 mph
        const baseWindSpeed = 3 + Math.random() * 17;
        
        // Weather affects wind speed
        const currentWeather = this.getCurrentWeather();
        let windMultiplier = 1;
        
        switch(currentWeather.name) {
            case 'sunny': windMultiplier = 0.7; break;
            case 'partly cloudy': windMultiplier = 0.9; break;
            case 'cloudy': windMultiplier = 1.1; break;
            case 'overcast': windMultiplier = 1.2; break;
            case 'light rain': windMultiplier = 1.3; break;
            case 'rain': windMultiplier = 1.5; break;
            case 'thunderstorm': windMultiplier = 2.0; break;
        }
        
        window.gameState.weather.wind = Math.round(baseWindSpeed * windMultiplier);
        
        // Occasionally change wind direction
        if (Math.random() < 0.2) {
            this.currentWindDirection = this.windDirections[Math.floor(Math.random() * this.windDirections.length)];
        }
    }
    
    // Update barometric pressure
    updatePressure() {
        // Pressure varies between 29.0 and 31.0 inches
        const basePressure = 29.0 + Math.random() * 2.0;
        
        // Weather affects pressure
        const currentWeather = this.getCurrentWeather();
        let pressureAdjustment = 0;
        
        switch(currentWeather.name) {
            case 'sunny': pressureAdjustment = 0.5; break;
            case 'partly cloudy': pressureAdjustment = 0.2; break;
            case 'cloudy': pressureAdjustment = 0; break;
            case 'overcast': pressureAdjustment = -0.2; break;
            case 'light rain': pressureAdjustment = -0.4; break;
            case 'rain': pressureAdjustment = -0.6; break;
            case 'thunderstorm': pressureAdjustment = -0.8; break;
        }
        
        window.gameState.weather.pressure = (basePressure + pressureAdjustment).toFixed(2);
    }
    
    // Get current weather object
    getCurrentWeather() {
        return this.weatherTypes[this.currentWeatherIndex];
    }
    
    // Update weather display in UI
    updateWeatherDisplay() {
        const currentWeather = this.getCurrentWeather();
        
        const weatherTypeElement = document.getElementById('weatherType');
        if (weatherTypeElement) {
            weatherTypeElement.textContent = `${currentWeather.icon} ${currentWeather.name}`;
        }
        
        const windElement = document.getElementById('wind');
        if (windElement) {
            windElement.textContent = `${window.gameState.weather.wind} mph ${this.currentWindDirection}`;
        }
        
        const pressureElement = document.getElementById('pressure');
        if (pressureElement) {
            pressureElement.textContent = `${window.gameState.weather.pressure} in`;
        }
    }
    
    // Update game state weather
    updateGameStateWeather() {
        const currentWeather = this.getCurrentWeather();
        window.gameState.weather.type = currentWeather.name;
    }
    
    // Check for weather effects on the game
    checkWeatherEffects() {
        const currentWeather = this.getCurrentWeather();
        
        // Rain affects water quality positively
        if (currentWeather.name === 'light rain' || currentWeather.name === 'rain') {
            window.gameState.oxygen += 0.1;
            window.gameState.oxygen = Math.min(12.0, window.gameState.oxygen);
            
            // Rain can slightly lower pH
            window.gameState.ph -= 0.02;
            window.gameState.ph = Math.max(6.0, window.gameState.ph);
        }
        
        // Thunderstorms can stress fish
        if (currentWeather.name === 'thunderstorm') {
            // Reduce fishing success temporarily
            if (window.fishingSystem && window.fishingSystem.isActive) {
                if (window.logger) {
                    window.logger.showAchievement('Thunderstorm! Fish are hiding deep!', '⛈️');
                }
            }
        }
        
        // Sunny weather can increase water temperature
        if (currentWeather.name === 'sunny') {
            window.gameState.temperature += 0.1;
            window.gameState.temperature = Math.min(85, window.gameState.temperature);
            
            // Hot weather can reduce oxygen
            if (window.gameState.temperature > 80) {
                window.gameState.oxygen -= 0.05;
                window.gameState.oxygen = Math.max(5.0, window.gameState.oxygen);
            }
        }
        
        // Cloudy weather is ideal for fishing
        if (currentWeather.name === 'overcast' || currentWeather.name === 'cloudy') {
            // Optimal fishing conditions - no special effects needed
            // The fishing multiplier is handled in the fishing system
        }
    }
    
    // Get fishing conditions rating
    getFishingConditions() {
        const currentWeather = this.getCurrentWeather();
        const windSpeed = window.gameState.weather.wind;
        const pressure = parseFloat(window.gameState.weather.pressure);
        
        let rating = 'Fair';
        let score = 50;
        
        // Weather score
        score += (currentWeather.fishingMultiplier - 1) * 50;
        
        // Wind score (light wind is better)
        if (windSpeed < 5) score += 20;
        else if (windSpeed < 10) score += 10;
        else if (windSpeed > 15) score -= 20;
        
        // Pressure score (falling pressure is better)
        if (pressure > 30.2) score += 15;
        else if (pressure < 29.8) score -= 15;
        
        // Determine rating
        if (score >= 80) rating = 'Excellent';
        else if (score >= 65) rating = 'Good';
        else if (score >= 35) rating = 'Fair';
        else rating = 'Poor';
        
        return {
            rating: rating,
            score: Math.max(0, Math.min(100, score)),
            multiplier: currentWeather.fishingMultiplier
        };
    }
    
    // Force weather change (for testing or special events)
    setWeather(weatherName) {
        const weatherIndex = this.weatherTypes.findIndex(w => w.name === weatherName);
        if (weatherIndex !== -1) {
            this.currentWeatherIndex = weatherIndex;
            this.updateWeatherDisplay();
            this.updateGameStateWeather();
            this.checkWeatherEffects();
            
            if (window.logger) {
                window.logger.logDebug('Weather', `Weather manually set to ${weatherName}`);
            }
        }
    }
    
    // Get weather forecast (next few weather changes)
    getForecast(hours = 3) {
        const forecast = [];
        let currentIndex = this.currentWeatherIndex;
        
        for (let i = 0; i < hours; i++) {
            // Simulate weather progression
            const change = Math.random() < 0.3 ? (Math.random() < 0.5 ? -1 : 1) : 0;
            currentIndex = Math.max(0, Math.min(this.weatherTypes.length - 1, currentIndex + change));
            
            forecast.push({
                hour: i + 1,
                weather: this.weatherTypes[currentIndex].name,
                icon: this.weatherTypes[currentIndex].icon
            });
        }
        
        return forecast;
    }
    
    // Activate weather system
    activate() {
        this.isActive = true;
        this.updateWeatherDisplay();
        
        if (window.logger) {
            window.logger.logDebug('Weather', 'Weather system activated');
        }
    }
    
    // Deactivate weather system
    deactivate() {
        this.isActive = false;
        
        if (window.logger) {
            window.logger.logDebug('Weather', 'Weather system deactivated');
        }
    }
    
    // Update weather system
    update() {
        if (!this.isActive) return;
        
        // Update displays
        this.updateWeatherDisplay();
        
        // Validate weather state
        if (!window.gameState.weather.type) {
            this.updateGameStateWeather();
        }
    }
}

// Initialize weather system
window.weatherSystem = new WeatherSystem();
