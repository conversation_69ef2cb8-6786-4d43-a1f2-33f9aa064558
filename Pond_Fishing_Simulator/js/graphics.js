// 3D Graphics and Scene Management - Texas Pond Fishing Simulator

class GraphicsEngine {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.boat = null;
        this.fishingRod = null;
        this.fishingLine = null;
        this.fishInPond = [];
        this.gameObjects = {
            terrain: null,
            pond: null,
            trees: [],
            dock: null,
            plants: []
        };
        this.isInitialized = false;
    }
    
    // Initialize Three.js components
    initialize() {
        return safeExecute(() => {
            if (window.logger) {
                window.logger.logDebug('Graphics', 'Initializing Three.js components');
            }
            
            // Scene setup
            this.scene = new THREE.Scene();
            this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
            
            // Camera setup
            this.camera = new THREE.PerspectiveCamera(
                75, 
                window.innerWidth / window.innerHeight, 
                0.1, 
                1000
            );
            this.camera.position.set(0, 15, 30);
            this.camera.lookAt(0, 0, 0);
            
            // Renderer setup
            const canvas = document.getElementById('gameCanvas');
            if (!canvas) {
                throw new Error('Canvas element not found');
            }
            
            this.renderer = new THREE.WebGLRenderer({ 
                canvas: canvas, 
                antialias: true,
                powerPreference: 'high-performance'
            });
            
            this.renderer.setSize(window.innerWidth, window.innerHeight);
            this.renderer.shadowMap.enabled = true;
            this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            this.renderer.setClearColor(0x87CEEB);
            
            // Store renderer globally for error recovery
            window.renderer = this.renderer;
            
            // WebGL context lost handler
            canvas.addEventListener('webglcontextlost', (e) => {
                if (window.logger) {
                    window.logger.logError('WebGL', 'Context lost');
                }
                e.preventDefault();
            });
            
            canvas.addEventListener('webglcontextrestored', () => {
                if (window.logger) {
                    window.logger.logDebug('WebGL', 'Context restored');
                }
                this.initializeScene();
            });
            
            // Window resize handler
            window.addEventListener('resize', () => {
                this.onWindowResize();
            });
            
            this.isInitialized = true;
            
            if (window.logger) {
                window.logger.logDebug('Graphics', 'Three.js initialization complete');
            }
            
            return true;
        }, 'Graphics Initialization');
    }
    
    // Initialize the 3D scene
    initializeScene() {
        return safeExecute(() => {
            if (window.logger) {
                window.logger.logDebug('Graphics', 'Initializing 3D scene');
            }
            
            // Clear existing scene
            while(this.scene.children.length > 0) {
                this.scene.remove(this.scene.children[0]);
            }
            
            // Setup lighting
            this.setupLighting();
            
            // Create environment
            this.createTerrain();
            this.createPond();
            this.createTrees();
            this.createSkybox();
            
            // Create game objects
            this.createBoat();
            
            // Initialize fish
            this.initializeFish();
            
            if (window.logger) {
                window.logger.logDebug('Graphics', 'Scene initialization complete');
            }
            
            return true;
        }, 'Scene Initialization');
    }
    
    // Setup lighting
    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        this.scene.add(directionalLight);
        
        // Point light for water reflection
        const pointLight = new THREE.PointLight(0x87CEEB, 0.3, 100);
        pointLight.position.set(0, 10, 0);
        this.scene.add(pointLight);
    }
    
    // Create terrain
    createTerrain() {
        const terrainGeometry = new THREE.PlaneGeometry(200, 200, 100, 100);
        
        // Add some height variation
        const vertices = terrainGeometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            vertices[i + 2] = Math.random() * 2 - 1; // Random height variation
        }
        terrainGeometry.attributes.position.needsUpdate = true;
        terrainGeometry.computeVertexNormals();
        
        const terrainMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x3d5c47,
            side: THREE.DoubleSide
        });
        
        const terrain = new THREE.Mesh(terrainGeometry, terrainMaterial);
        terrain.rotation.x = -Math.PI / 2;
        terrain.position.y = -2;
        terrain.receiveShadow = true;
        terrain.name = 'terrain';
        
        this.scene.add(terrain);
        this.gameObjects.terrain = terrain;
    }
    
    // Create pond
    createPond() {
        // Main pond surface
        const pondGeometry = new THREE.CircleGeometry(25, 64);
        const pondMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x1e6091,
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        });
        
        const pond = new THREE.Mesh(pondGeometry, pondMaterial);
        pond.rotation.x = -Math.PI / 2;
        pond.position.y = -0.5;
        pond.receiveShadow = true;
        pond.name = 'pond';
        
        this.scene.add(pond);
        this.gameObjects.pond = pond;
        
        // Pond edge
        const edgeGeometry = new THREE.RingGeometry(25, 27, 64);
        const edgeMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x8b7355,
            side: THREE.DoubleSide
        });
        
        const edge = new THREE.Mesh(edgeGeometry, edgeMaterial);
        edge.rotation.x = -Math.PI / 2;
        edge.position.y = -1.5;
        edge.receiveShadow = true;
        edge.name = 'pond-edge';
        
        this.scene.add(edge);
        
        // Add water animation
        this.animateWater();
    }
    
    // Create trees around the pond
    createTrees() {
        const treeCount = 30;
        this.gameObjects.trees = [];
        
        for (let i = 0; i < treeCount; i++) {
            const treeGroup = new THREE.Group();
            treeGroup.name = `tree-${i}`;
            
            // Trunk
            const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, 8, 8);
            const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8b4513 });
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
            trunk.position.y = 2;
            trunk.castShadow = true;
            trunk.receiveShadow = true;
            treeGroup.add(trunk);
            
            // Foliage
            const foliageGeometry = new THREE.SphereGeometry(4, 8, 6);
            const foliageMaterial = new THREE.MeshLambertMaterial({ 
                color: new THREE.Color().setHSL(0.3, 0.6, 0.3 + Math.random() * 0.2)
            });
            const foliage = new THREE.Mesh(foliageGeometry, foliageMaterial);
            foliage.position.y = 7;
            foliage.castShadow = true;
            foliage.receiveShadow = true;
            treeGroup.add(foliage);
            
            // Position randomly but not in pond
            let x, z, attempts = 0;
            do {
                x = (Math.random() - 0.5) * 180;
                z = (Math.random() - 0.5) * 180;
                attempts++;
            } while (Math.sqrt(x*x + z*z) < 35 && attempts < 100);
            
            if (attempts < 100) {
                treeGroup.position.set(x, -2, z);
                this.scene.add(treeGroup);
                this.gameObjects.trees.push(treeGroup);
            }
        }
    }
    
    // Create skybox
    createSkybox() {
        const skyGeometry = new THREE.SphereGeometry(500, 32, 32);
        const skyMaterial = new THREE.MeshBasicMaterial({
            color: 0x87CEEB,
            side: THREE.BackSide
        });
        
        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }
    
    // Create boat
    createBoat() {
        const boatGroup = new THREE.Group();
        boatGroup.name = 'boat';
        
        // Hull
        const hullGeometry = new THREE.BoxGeometry(6, 1, 2);
        const hullMaterial = new THREE.MeshLambertMaterial({ color: 0x8b4513 });
        const hull = new THREE.Mesh(hullGeometry, hullMaterial);
        hull.position.y = 0.5;
        hull.castShadow = true;
        hull.receiveShadow = true;
        boatGroup.add(hull);
        
        // Seats
        const seatGeometry = new THREE.BoxGeometry(1.5, 0.5, 1.5);
        const seatMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        
        const seat1 = new THREE.Mesh(seatGeometry, seatMaterial);
        seat1.position.set(-1.5, 1.2, 0);
        seat1.castShadow = true;
        boatGroup.add(seat1);
        
        const seat2 = new THREE.Mesh(seatGeometry, seatMaterial);
        seat2.position.set(1.5, 1.2, 0);
        seat2.castShadow = true;
        boatGroup.add(seat2);
        
        // Position boat
        boatGroup.position.set(0, 0, 20);
        this.scene.add(boatGroup);
        this.boat = boatGroup;
        
        // Create fishing rod
        this.createFishingRod();
    }
    
    // Create fishing rod
    createFishingRod() {
        const rodGroup = new THREE.Group();
        
        // Rod
        const rodGeometry = new THREE.CylinderGeometry(0.05, 0.08, 8, 8);
        const rodMaterial = new THREE.MeshLambertMaterial({ color: 0x8b4513 });
        const rod = new THREE.Mesh(rodGeometry, rodMaterial);
        rod.position.y = 4;
        rod.rotation.z = Math.PI / 6;
        rodGroup.add(rod);
        
        // Reel
        const reelGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.5, 16);
        const reelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const reel = new THREE.Mesh(reelGeometry, reelMaterial);
        reel.position.set(0, 2, 0);
        reel.rotation.x = Math.PI / 2;
        rodGroup.add(reel);
        
        // Add to boat
        rodGroup.position.set(2, 1, 0);
        this.boat.add(rodGroup);
        this.fishingRod = rodGroup;
    }
    
    // Initialize fish in the pond
    initializeFish() {
        this.fishInPond = [];
        
        // Create fish based on game state
        Object.keys(window.gameState.fishPopulation).forEach(fishType => {
            const count = window.gameState.fishPopulation[fishType];
            for (let i = 0; i < count; i++) {
                this.createFish(fishType);
            }
        });
    }
    
    // Create a single fish
    createFish(fishType) {
        const fishGroup = new THREE.Group();
        
        // Fish body
        const bodyGeometry = new THREE.SphereGeometry(0.5, 8, 6);
        bodyGeometry.scale(2, 1, 1); // Make it fish-shaped
        
        let bodyColor;
        switch(fishType) {
            case 'bass': bodyColor = 0x2d5016; break;
            case 'catfish': bodyColor = 0x4a4a4a; break;
            case 'pike': bodyColor = 0x556b2f; break;
            case 'bluegill': bodyColor = 0x4169e1; break;
            case 'minnows': bodyColor = 0xc0c0c0; break;
            case 'shad': bodyColor = 0x708090; break;
            default: bodyColor = 0x888888;
        }
        
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: bodyColor });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        fishGroup.add(body);
        
        // Tail
        const tailGeometry = new THREE.ConeGeometry(0.3, 1, 6);
        const tail = new THREE.Mesh(tailGeometry, bodyMaterial);
        tail.position.set(-1.5, 0, 0);
        tail.rotation.z = Math.PI / 2;
        fishGroup.add(tail);
        
        // Position randomly in pond
        const angle = Math.random() * Math.PI * 2;
        const radius = Math.random() * 20;
        fishGroup.position.set(
            Math.cos(angle) * radius,
            -2 + Math.random() * 3, // Depth variation
            Math.sin(angle) * radius
        );
        
        // Add swimming behavior
        fishGroup.userData = {
            type: fishType,
            speed: 0.1 + Math.random() * 0.2,
            direction: Math.random() * Math.PI * 2,
            depth: fishGroup.position.y,
            targetDepth: fishGroup.position.y
        };
        
        this.scene.add(fishGroup);
        this.fishInPond.push(fishGroup);
        
        return fishGroup;
    }
    
    // Animate water surface
    animateWater() {
        if (this.gameObjects.pond) {
            const time = Date.now() * 0.001;
            this.gameObjects.pond.material.opacity = 0.7 + Math.sin(time) * 0.1;
        }
    }
    
    // Animate fish swimming
    animateFish() {
        this.fishInPond.forEach(fish => {
            if (!fish.userData) return;
            
            const userData = fish.userData;
            
            // Update direction occasionally
            if (Math.random() < 0.02) {
                userData.direction += (Math.random() - 0.5) * 0.5;
                userData.targetDepth = -2 + Math.random() * 3;
            }
            
            // Move fish
            fish.position.x += Math.cos(userData.direction) * userData.speed;
            fish.position.z += Math.sin(userData.direction) * userData.speed;
            
            // Adjust depth
            fish.position.y += (userData.targetDepth - fish.position.y) * 0.02;
            
            // Keep fish in pond bounds
            const distanceFromCenter = Math.sqrt(fish.position.x * fish.position.x + fish.position.z * fish.position.z);
            if (distanceFromCenter > 22) {
                userData.direction += Math.PI; // Turn around
            }
            
            // Rotate fish to face movement direction
            fish.rotation.y = userData.direction;
        });
    }
    
    // Handle window resize
    onWindowResize() {
        if (this.camera && this.renderer) {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        }
    }
    
    // Main render loop
    render() {
        if (!this.isInitialized || !this.renderer || !this.scene || !this.camera) {
            return;
        }
        
        // Animate elements
        this.animateWater();
        this.animateFish();
        
        // Update camera position based on game mode
        if (window.gameState && window.gameState.mode === 'fishing' && this.boat) {
            // Follow boat camera
            const boatPosition = this.boat.position;
            this.camera.position.set(
                boatPosition.x - 10,
                boatPosition.y + 8,
                boatPosition.z + 15
            );
            this.camera.lookAt(boatPosition);
        }
        
        // Render the scene
        this.renderer.render(this.scene, this.camera);
        
        // Update FPS counter
        if (window.logger) {
            window.logger.updateFPS();
        }
    }
    
    // Add dock to pond
    addDock() {
        if (this.gameObjects.dock) return; // Already exists
        
        const dockGroup = new THREE.Group();
        
        // Dock platform
        const platformGeometry = new THREE.BoxGeometry(12, 0.5, 4);
        const platformMaterial = new THREE.MeshLambertMaterial({ color: 0x8b7355 });
        const platform = new THREE.Mesh(platformGeometry, platformMaterial);
        platform.position.y = 0.25;
        platform.castShadow = true;
        platform.receiveShadow = true;
        dockGroup.add(platform);
        
        // Support posts
        for (let i = -4; i <= 4; i += 4) {
            const postGeometry = new THREE.CylinderGeometry(0.2, 0.2, 3, 8);
            const postMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
            const post = new THREE.Mesh(postGeometry, postMaterial);
            post.position.set(i, -1.5, 0);
            post.castShadow = true;
            dockGroup.add(post);
        }
        
        // Position dock
        dockGroup.position.set(25, -0.5, 0);
        this.scene.add(dockGroup);
        this.gameObjects.dock = dockGroup;
        
        if (window.logger) {
            window.logger.logDebug('Graphics', 'Dock added to scene');
        }
    }
    
    // Add aquatic plants
    addPlants(count = 10) {
        for (let i = 0; i < count; i++) {
            const plantGroup = new THREE.Group();
            
            // Plant stem
            const stemGeometry = new THREE.CylinderGeometry(0.1, 0.1, 3, 6);
            const stemMaterial = new THREE.MeshLambertMaterial({ color: 0x228b22 });
            const stem = new THREE.Mesh(stemGeometry, stemMaterial);
            stem.position.y = 1.5;
            plantGroup.add(stem);
            
            // Plant leaves
            for (let j = 0; j < 3; j++) {
                const leafGeometry = new THREE.SphereGeometry(0.5, 6, 4);
                leafGeometry.scale(2, 0.1, 1);
                const leafMaterial = new THREE.MeshLambertMaterial({ color: 0x32cd32 });
                const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
                leaf.position.set(
                    (Math.random() - 0.5) * 2,
                    j + 1,
                    (Math.random() - 0.5) * 2
                );
                leaf.rotation.y = Math.random() * Math.PI * 2;
                plantGroup.add(leaf);
            }
            
            // Position randomly in pond
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 20;
            plantGroup.position.set(
                Math.cos(angle) * radius,
                -2,
                Math.sin(angle) * radius
            );
            
            this.scene.add(plantGroup);
            this.gameObjects.plants.push(plantGroup);
        }
        
        if (window.logger) {
            window.logger.logDebug('Graphics', `Added ${count} aquatic plants`);
        }
    }
}

// Initialize graphics engine
window.graphicsEngine = new GraphicsEngine();
