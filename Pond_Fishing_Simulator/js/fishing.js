// Fishing Mechanics - Texas Pond Fishing Simulator

class FishingSystem {
    constructor() {
        this.isActive = false;
        this.castPowerBuilding = false;
        this.castPowerDirection = 1;
        this.fishingLine = null;
        this.hookedFish = null;
        this.reelSpeed = 0;
        this.keys = {};
        this.mouse = { x: 0, y: 0 };
        this.castDirection = { x: 0, z: 1 };
        
        this.setupEventListeners();
    }
    
    // Setup input event listeners
    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (event) => {
            this.keys[event.code] = true;
            this.handleKeyDown(event);
        });
        
        document.addEventListener('keyup', (event) => {
            this.keys[event.code] = false;
        });
        
        // Mouse events
        document.addEventListener('mousemove', (event) => {
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            this.updateCastDirection();
        });
        
        document.addEventListener('mousedown', (event) => {
            if (event.button === 0) { // Left click
                this.handleMouseDown();
            }
        });
        
        document.addEventListener('mouseup', (event) => {
            if (event.button === 0) { // Left click
                this.handleMouseUp();
            }
        });
        
        // Cast button
        const castButton = document.getElementById('castButton');
        if (castButton) {
            castButton.addEventListener('click', () => {
                this.startCasting();
            });
        }
    }
    
    // Handle key down events
    handleKeyDown(event) {
        if (!this.isActive) return;
        
        switch(event.code) {
            case 'Space':
                event.preventDefault();
                this.startCasting();
                break;
            case 'KeyR':
                this.startReeling();
                break;
            case 'Escape':
                this.cancelFishing();
                break;
        }
    }
    
    // Handle mouse down
    handleMouseDown() {
        if (!this.isActive) return;
        
        if (window.gameState.fishingState.lineInWater) {
            this.startReeling();
        }
    }
    
    // Handle mouse up
    handleMouseUp() {
        if (!this.isActive) return;
        
        if (window.gameState.fishingState.reeling) {
            this.stopReeling();
        }
    }
    
    // Update cast direction based on mouse position
    updateCastDirection() {
        if (!this.isActive) return;
        
        // Convert mouse position to world direction
        const angle = Math.atan2(this.mouse.x, this.mouse.y);
        this.castDirection.x = Math.sin(angle);
        this.castDirection.z = Math.cos(angle);
    }
    
    // Start casting process
    startCasting() {
        if (!this.isActive || window.gameState.fishingState.casting || window.gameState.fishingState.lineInWater) {
            return;
        }
        
        if (window.logger) {
            window.logger.logDebug('Fishing', 'Starting cast');
        }
        
        window.gameState.fishingState.casting = true;
        this.castPowerBuilding = true;
        this.buildCastPower();
    }
    
    // Build casting power
    buildCastPower() {
        if (!this.castPowerBuilding) return;
        
        const powerIncrement = 2 * this.castPowerDirection;
        window.gameState.fishingState.castPower += powerIncrement;
        
        // Reverse direction at limits
        if (window.gameState.fishingState.castPower >= 100) {
            window.gameState.fishingState.castPower = 100;
            this.castPowerDirection = -1;
        } else if (window.gameState.fishingState.castPower <= 0) {
            window.gameState.fishingState.castPower = 0;
            this.castPowerDirection = 1;
        }
        
        // Auto-cast after 3 seconds
        setTimeout(() => {
            if (this.castPowerBuilding) {
                this.executeCast();
            }
        }, 3000);
        
        // Continue building power
        if (this.castPowerBuilding) {
            setTimeout(() => this.buildCastPower(), 50);
        }
    }
    
    // Execute the cast
    executeCast() {
        if (!window.gameState.fishingState.casting) return;
        
        this.castPowerBuilding = false;
        
        const power = window.gameState.fishingState.castPower;
        const distance = (power / 100) * 40; // Max distance 40 units
        
        // Calculate cast position
        const boatPosition = window.graphicsEngine.boat.position;
        const castX = boatPosition.x + this.castDirection.x * distance;
        const castZ = boatPosition.z + this.castDirection.z * distance;
        
        // Check if cast is within pond bounds
        const distanceFromPondCenter = Math.sqrt(castX * castX + castZ * castZ);
        if (distanceFromPondCenter > 25) {
            // Cast missed the pond
            if (window.logger) {
                window.logger.logDebug('Fishing', 'Cast missed the pond');
            }
            this.resetFishingState();
            return;
        }
        
        // Create fishing line
        this.createFishingLine(castX, castZ);
        
        // Update fishing state
        window.gameState.fishingState.casting = false;
        window.gameState.fishingState.lineInWater = true;
        window.gameState.fishingState.depth = Math.random() * 5 + 2; // 2-7 feet deep
        
        // Start fishing simulation
        this.startFishingSimulation();
        
        if (window.logger) {
            window.logger.logDebug('Fishing', `Cast executed with ${power}% power to (${castX.toFixed(1)}, ${castZ.toFixed(1)})`);
        }
    }
    
    // Create visual fishing line
    createFishingLine(targetX, targetZ) {
        if (this.fishingLine) {
            window.graphicsEngine.scene.remove(this.fishingLine);
        }
        
        const boatPosition = window.graphicsEngine.boat.position;
        const rodTip = new THREE.Vector3(
            boatPosition.x + 2,
            boatPosition.y + 6,
            boatPosition.z
        );
        
        const lineEnd = new THREE.Vector3(targetX, -1, targetZ);
        
        const lineGeometry = new THREE.BufferGeometry().setFromPoints([rodTip, lineEnd]);
        const lineMaterial = new THREE.LineBasicMaterial({ color: 0x333333 });
        
        this.fishingLine = new THREE.Line(lineGeometry, lineMaterial);
        window.graphicsEngine.scene.add(this.fishingLine);
    }
    
    // Start fishing simulation
    startFishingSimulation() {
        // Check for fish in the area
        setTimeout(() => {
            this.checkForFishBite();
        }, 2000 + Math.random() * 8000); // 2-10 seconds
    }
    
    // Check if a fish bites
    checkForFishBite() {
        if (!window.gameState.fishingState.lineInWater) return;
        
        // Calculate bite probability based on fish population and conditions
        const totalFish = Object.values(window.gameState.fishPopulation).reduce((sum, count) => sum + count, 0);
        const baseProbability = Math.min(0.7, totalFish * 0.02); // Max 70% chance
        
        // Weather affects bite rate
        let weatherMultiplier = 1;
        switch(window.gameState.weather.type) {
            case 'overcast': weatherMultiplier = 1.3; break;
            case 'light rain': weatherMultiplier = 1.5; break;
            case 'rain': weatherMultiplier = 0.8; break;
            case 'sunny': weatherMultiplier = 0.9; break;
        }
        
        const finalProbability = baseProbability * weatherMultiplier;
        
        if (Math.random() < finalProbability) {
            this.fishBites();
        } else {
            // No bite, try again
            setTimeout(() => {
                this.checkForFishBite();
            }, 3000 + Math.random() * 5000);
        }
    }
    
    // Fish bites the hook
    fishBites() {
        if (!window.gameState.fishingState.lineInWater) return;
        
        // Determine which type of fish bit
        const availableFish = [];
        Object.keys(window.gameState.fishPopulation).forEach(fishType => {
            const count = window.gameState.fishPopulation[fishType];
            for (let i = 0; i < count; i++) {
                availableFish.push(fishType);
            }
        });
        
        if (availableFish.length === 0) return;
        
        const caughtFishType = availableFish[Math.floor(Math.random() * availableFish.length)];
        
        // Create fish data
        this.hookedFish = {
            type: caughtFishType,
            size: this.generateFishSize(caughtFishType),
            fight: this.generateFishFight(caughtFishType),
            stamina: 100
        };
        
        window.gameState.fishingState.fishHooked = true;
        window.gameState.fishingState.fishFighting = true;
        
        // Start fish fighting simulation
        this.startFishFight();
        
        if (window.logger) {
            window.logger.logDebug('Fishing', `${caughtFishType} bit the hook! Size: ${this.hookedFish.size.toFixed(1)} lbs`);
        }
    }
    
    // Generate fish size based on type
    generateFishSize(fishType) {
        const sizeRanges = {
            minnows: [0.1, 0.3],
            bluegill: [0.5, 2.0],
            shad: [0.3, 1.5],
            catfish: [2.0, 15.0],
            bass: [1.0, 8.0],
            pike: [3.0, 20.0]
        };
        
        const range = sizeRanges[fishType] || [1, 5];
        return range[0] + Math.random() * (range[1] - range[0]);
    }
    
    // Generate fish fighting strength
    generateFishFight(fishType) {
        const fightStrengths = {
            minnows: 1,
            bluegill: 2,
            shad: 2,
            catfish: 4,
            bass: 5,
            pike: 6
        };
        
        return fightStrengths[fishType] || 3;
    }
    
    // Activate fishing system
    activate() {
        this.isActive = true;
        
        if (window.logger) {
            window.logger.logDebug('Fishing', 'Fishing system activated');
        }
    }
    
    // Deactivate fishing system
    deactivate() {
        this.isActive = false;
        this.resetFishingState();
        
        if (window.logger) {
            window.logger.logDebug('Fishing', 'Fishing system deactivated');
        }
    }
    
    // Start fish fighting mechanics
    startFishFight() {
        this.fightInterval = setInterval(() => {
            this.updateFishFight();
        }, 100);
    }

    // Update fish fighting
    updateFishFight() {
        if (!this.hookedFish || !window.gameState.fishingState.fishFighting) {
            this.stopFishFight();
            return;
        }

        // Fish tries to escape
        const escapeForce = this.hookedFish.fight * (this.hookedFish.stamina / 100);

        // Player reeling force
        const reelForce = window.gameState.fishingState.reeling ? this.reelSpeed : 0;

        // Calculate tension
        const tensionChange = escapeForce - reelForce;
        window.gameState.fishingState.lineTension += tensionChange;
        window.gameState.fishingState.lineTension = Math.max(0, Math.min(100, window.gameState.fishingState.lineTension));

        // Check if line breaks
        if (window.gameState.fishingState.lineTension >= 100) {
            this.lineBreaks();
            return;
        }

        // Reduce fish stamina when reeling
        if (window.gameState.fishingState.reeling && reelForce > escapeForce) {
            this.hookedFish.stamina -= 2;
        }

        // Fish caught when stamina reaches 0
        if (this.hookedFish.stamina <= 0) {
            this.fishCaught();
            return;
        }

        // Fish might escape if tension is too low for too long
        if (window.gameState.fishingState.lineTension < 10) {
            this.hookedFish.stamina += 0.5; // Fish recovers
            if (this.hookedFish.stamina > 120) {
                this.fishEscapes();
                return;
            }
        }
    }

    // Start reeling
    startReeling() {
        if (!window.gameState.fishingState.lineInWater) return;

        window.gameState.fishingState.reeling = true;
        this.reelSpeed = 3;

        if (window.logger) {
            window.logger.logDebug('Fishing', 'Started reeling');
        }
    }

    // Stop reeling
    stopReeling() {
        window.gameState.fishingState.reeling = false;
        this.reelSpeed = 0;

        if (window.logger) {
            window.logger.logDebug('Fishing', 'Stopped reeling');
        }
    }

    // Fish is successfully caught
    fishCaught() {
        if (!this.hookedFish) return;

        const fishType = this.hookedFish.type;
        const fishSize = this.hookedFish.size;

        // Remove fish from pond
        window.gameState.removeFish(fishType, 1);

        // Update statistics
        if (fishSize > window.gameState.largestFish) {
            window.gameState.largestFish = fishSize;
            if (window.logger) {
                window.logger.showAchievement(`New Record! ${fishSize.toFixed(1)} lb ${fishType}!`, '🏆');
            }
        }

        // Show success message
        if (window.logger) {
            window.logger.logDebug('Fishing', `Caught a ${fishSize.toFixed(1)} lb ${fishType}!`);
            window.logger.showAchievement(`Caught ${fishSize.toFixed(1)} lb ${fishType}!`, '🎣');
        }

        this.resetFishingState();
    }

    // Line breaks
    lineBreaks() {
        if (window.logger) {
            window.logger.logDebug('Fishing', 'Line broke! Fish escaped.');
            window.logger.showAchievement('Line broke! Fish got away...', '💔');
        }

        this.resetFishingState();
    }

    // Fish escapes
    fishEscapes() {
        if (window.logger) {
            window.logger.logDebug('Fishing', 'Fish escaped!');
            window.logger.showAchievement('Fish got away!', '🐟');
        }

        this.resetFishingState();
    }

    // Stop fish fight
    stopFishFight() {
        if (this.fightInterval) {
            clearInterval(this.fightInterval);
            this.fightInterval = null;
        }
    }

    // Cancel fishing
    cancelFishing() {
        this.resetFishingState();

        if (window.logger) {
            window.logger.logDebug('Fishing', 'Fishing cancelled');
        }
    }

    // Update boat movement
    updateBoatMovement() {
        if (!this.isActive || !window.graphicsEngine.boat) return;

        const boat = window.graphicsEngine.boat;
        const moveSpeed = 0.2;

        // WASD movement
        if (this.keys['KeyW'] || this.keys['ArrowUp']) {
            boat.position.z -= moveSpeed;
        }
        if (this.keys['KeyS'] || this.keys['ArrowDown']) {
            boat.position.z += moveSpeed;
        }
        if (this.keys['KeyA'] || this.keys['ArrowLeft']) {
            boat.position.x -= moveSpeed;
        }
        if (this.keys['KeyD'] || this.keys['ArrowRight']) {
            boat.position.x += moveSpeed;
        }

        // Keep boat in bounds
        const maxDistance = 30;
        const distanceFromCenter = Math.sqrt(boat.position.x * boat.position.x + boat.position.z * boat.position.z);
        if (distanceFromCenter > maxDistance) {
            const angle = Math.atan2(boat.position.z, boat.position.x);
            boat.position.x = Math.cos(angle) * maxDistance;
            boat.position.z = Math.sin(angle) * maxDistance;
        }
    }

    // Update fishing system
    update() {
        if (!this.isActive) return;

        this.updateBoatMovement();

        // Update fishing line position if it exists
        if (this.fishingLine && window.graphicsEngine.boat) {
            const boatPosition = window.graphicsEngine.boat.position;
            const rodTip = new THREE.Vector3(
                boatPosition.x + 2,
                boatPosition.y + 6,
                boatPosition.z
            );

            const positions = this.fishingLine.geometry.attributes.position.array;
            positions[0] = rodTip.x;
            positions[1] = rodTip.y;
            positions[2] = rodTip.z;

            this.fishingLine.geometry.attributes.position.needsUpdate = true;
        }
    }

    // Reset fishing state
    resetFishingState() {
        this.castPowerBuilding = false;
        this.hookedFish = null;
        this.reelSpeed = 0;

        window.gameState.fishingState.casting = false;
        window.gameState.fishingState.reeling = false;
        window.gameState.fishingState.fishHooked = false;
        window.gameState.fishingState.lineTension = 0;
        window.gameState.fishingState.castPower = 50;
        window.gameState.fishingState.fishFighting = false;
        window.gameState.fishingState.lineInWater = false;
        window.gameState.fishingState.depth = 0;

        this.stopFishFight();

        // Remove fishing line
        if (this.fishingLine) {
            window.graphicsEngine.scene.remove(this.fishingLine);
            this.fishingLine = null;
        }
    }
}

// Initialize fishing system
window.fishingSystem = new FishingSystem();
