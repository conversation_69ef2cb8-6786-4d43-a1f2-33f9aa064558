// Main Game Controller - Texas Pond Fishing Simulator

class PondFishingGame {
    constructor() {
        this.isInitialized = false;
        this.isRunning = false;
        this.animationId = null;
        this.lastUpdateTime = 0;
        this.loadingProgress = 0;
        
        this.initializationSteps = [
            { name: 'Loading game systems...', duration: 500 },
            { name: 'Initializing 3D graphics...', duration: 1000 },
            { name: 'Creating pond environment...', duration: 800 },
            { name: 'Setting up weather system...', duration: 300 },
            { name: 'Preparing fishing mechanics...', duration: 400 },
            { name: 'Loading saved game...', duration: 200 },
            { name: 'Finalizing setup...', duration: 300 }
        ];
        
        this.currentStep = 0;
    }
    
    // Initialize the game
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // Show loading screen
            this.showLoadingScreen();
            
            // Wait for DOM to be ready
            if (document.readyState !== 'complete') {
                await new Promise(resolve => {
                    window.addEventListener('load', resolve);
                });
            }
            
            // Initialize systems step by step
            for (let i = 0; i < this.initializationSteps.length; i++) {
                this.currentStep = i;
                const step = this.initializationSteps[i];
                
                this.updateLoadingProgress(step.name, (i / this.initializationSteps.length) * 100);
                
                await this.executeInitializationStep(i);
                await this.delay(step.duration);
            }
            
            // Complete initialization
            this.updateLoadingProgress('Game ready!', 100);
            await this.delay(500);
            
            this.isInitialized = true;
            this.hideLoadingScreen();
            this.start();
            
            if (window.logger) {
                window.logger.logDebug('Game', 'Game initialization complete');
            }
            
        } catch (error) {
            if (window.logger) {
                window.logger.logError('Game Initialization', error.message);
            }
            this.showError('Failed to initialize game: ' + error.message);
        }
    }
    
    // Execute a specific initialization step
    async executeInitializationStep(stepIndex) {
        switch(stepIndex) {
            case 0: // Loading game systems
                this.setupEventListeners();
                break;
                
            case 1: // Initializing 3D graphics
                if (window.graphicsEngine) {
                    await window.graphicsEngine.initialize();
                }
                break;
                
            case 2: // Creating pond environment
                if (window.graphicsEngine) {
                    await window.graphicsEngine.initializeScene();
                }
                break;
                
            case 3: // Setting up weather system
                if (window.weatherSystem) {
                    window.weatherSystem.activate();
                }
                break;
                
            case 4: // Preparing fishing mechanics
                if (window.fishingSystem && window.stockingSystem) {
                    // Systems are ready, activate stocking mode by default
                    window.stockingSystem.activate();
                }
                break;
                
            case 5: // Loading saved game
                if (window.gameState) {
                    window.gameState.load();
                }
                break;
                
            case 6: // Finalizing setup
                this.finalizeSetup();
                break;
        }
    }
    
    // Setup global event listeners
    setupEventListeners() {
        // Mode toggle button
        const modeToggle = document.getElementById('modeToggle');
        if (modeToggle) {
            modeToggle.addEventListener('click', () => {
                this.toggleMode();
            });
        }
        
        // Auto-save every 30 seconds
        setInterval(() => {
            if (window.gameState && this.isRunning) {
                window.gameState.save();
            }
        }, 30000);
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
        
        // Handle window beforeunload
        window.addEventListener('beforeunload', () => {
            if (window.gameState) {
                window.gameState.save();
            }
        });
    }
    
    // Finalize game setup
    finalizeSetup() {
        // Update UI with initial state
        if (window.gameState) {
            window.gameState.updateUI();
        }
        
        // Perform initial health check
        if (window.logger) {
            window.logger.performHealthCheck();
        }
        
        // Initialize fish in scene based on saved state
        if (window.graphicsEngine && window.gameState) {
            window.graphicsEngine.initializeFish();
        }
    }
    
    // Start the game loop
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.lastUpdateTime = performance.now();
        this.gameLoop();
        
        if (window.logger) {
            window.logger.logDebug('Game', 'Game started');
        }
    }
    
    // Pause the game
    pause() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        
        if (window.logger) {
            window.logger.logDebug('Game', 'Game paused');
        }
    }
    
    // Resume the game
    resume() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.lastUpdateTime = performance.now();
        this.gameLoop();
        
        if (window.logger) {
            window.logger.logDebug('Game', 'Game resumed');
        }
    }
    
    // Main game loop
    gameLoop() {
        if (!this.isRunning) return;
        
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastUpdateTime;
        this.lastUpdateTime = currentTime;
        
        // Update game systems
        this.update(deltaTime);
        
        // Render the scene
        this.render();
        
        // Schedule next frame
        this.animationId = requestAnimationFrame(() => this.gameLoop());
    }
    
    // Update all game systems
    update(deltaTime) {
        try {
            // Update game state
            if (window.gameState) {
                window.gameState.updateUI();
            }
            
            // Update weather system
            if (window.weatherSystem) {
                window.weatherSystem.update();
            }
            
            // Update active game mode system
            if (window.gameState && window.gameState.mode === 'fishing') {
                if (window.fishingSystem) {
                    window.fishingSystem.update();
                }
            } else {
                if (window.stockingSystem) {
                    window.stockingSystem.update();
                }
            }
            
            // Validate game state periodically
            if (Math.random() < 0.01) { // 1% chance per frame
                if (window.gameState) {
                    window.gameState.validate();
                }
            }
            
        } catch (error) {
            if (window.logger) {
                window.logger.logError('Game Update', error.message);
            }
        }
    }
    
    // Render the game
    render() {
        try {
            if (window.graphicsEngine) {
                window.graphicsEngine.render();
            }
        } catch (error) {
            if (window.logger) {
                window.logger.logError('Game Render', error.message);
            }
        }
    }
    
    // Toggle between stocking and fishing modes
    toggleMode() {
        if (!window.gameState) return;
        
        const oldMode = window.gameState.mode;
        window.gameState.switchMode();
        const newMode = window.gameState.mode;
        
        // Activate/deactivate appropriate systems
        if (newMode === 'fishing') {
            if (window.stockingSystem) window.stockingSystem.deactivate();
            if (window.fishingSystem) window.fishingSystem.activate();
        } else {
            if (window.fishingSystem) window.fishingSystem.deactivate();
            if (window.stockingSystem) window.stockingSystem.activate();
        }
        
        if (window.logger) {
            window.logger.logDebug('Game', `Mode changed from ${oldMode} to ${newMode}`);
        }
    }
    
    // Show loading screen
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }
    
    // Hide loading screen
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }
    
    // Update loading progress
    updateLoadingProgress(text, percentage) {
        const progressText = document.getElementById('progressText');
        const progressFill = document.getElementById('progressFill');
        
        if (progressText) {
            progressText.textContent = text;
        }
        
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
        
        this.loadingProgress = percentage;
    }
    
    // Show error message
    showError(message) {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div style="text-align: center; color: #e74c3c;">
                    <h2>⚠️ Error</h2>
                    <p>${message}</p>
                    <button onclick="location.reload()" style="
                        background: #3498db; 
                        color: white; 
                        border: none; 
                        padding: 10px 20px; 
                        border-radius: 5px; 
                        cursor: pointer;
                        margin-top: 20px;
                    ">Reload Game</button>
                </div>
            `;
        }
    }
    
    // Utility function for delays
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Get game statistics
    getGameStats() {
        if (!window.gameState) return null;
        
        const totalFish = Object.values(window.gameState.fishPopulation).reduce((sum, count) => sum + count, 0);
        const ecosystemBalance = window.gameState.calculateEcosystemBalance();
        
        return {
            budget: window.gameState.budget,
            totalFish: totalFish,
            fishCaught: window.gameState.totalFishCaught,
            largestFish: window.gameState.largestFish,
            ecosystemBalance: ecosystemBalance,
            achievements: window.gameState.achievements.length,
            mode: window.gameState.mode,
            weather: window.gameState.weather.type
        };
    }
    
    // Reset game to initial state
    resetGame() {
        if (confirm('Are you sure you want to reset the game? All progress will be lost.')) {
            localStorage.removeItem('pondFishingSimulator');
            location.reload();
        }
    }
}

// Initialize and start the game when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.game = new PondFishingGame();
    
    // Start initialization after a short delay to ensure all scripts are loaded
    setTimeout(() => {
        window.game.initialize();
    }, 100);
});

// Global error handler for the game
window.addEventListener('error', (event) => {
    console.error('Global game error:', event.error);
    
    if (window.logger) {
        window.logger.logError('Global', event.error ? event.error.message : 'Unknown error');
    }
});

// Export game instance for debugging
window.PondFishingGame = PondFishingGame;
