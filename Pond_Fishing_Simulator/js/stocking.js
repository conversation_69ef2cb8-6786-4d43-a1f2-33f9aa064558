// Stocking System - Texas Pond Fishing Simulator

class StockingSystem {
    constructor() {
        this.isActive = false;
        this.fishPrices = {
            bluegill: 50,
            minnows: 30,
            shad: 40,
            bass: 150,
            catfish: 80,
            pike: 120
        };
        
        this.improvementPrices = {
            plants: 100,
            dock: 500,
            treatment: 200
        };
        
        this.setupEventListeners();
    }
    
    // Setup event listeners for stocking buttons
    setupEventListeners() {
        // Fish stocking buttons
        document.querySelectorAll('.stock-button[data-fish]').forEach(button => {
            button.addEventListener('click', (event) => {
                const fishType = event.currentTarget.getAttribute('data-fish');
                const cost = parseInt(event.currentTarget.getAttribute('data-cost'));
                this.stockFish(fishType, cost);
            });
        });
        
        // Improvement buttons
        const plantsBtn = document.getElementById('plantsBtn');
        if (plantsBtn) {
            plantsBtn.addEventListener('click', () => {
                this.addPlants();
            });
        }
        
        const dockBtn = document.getElementById('dockBtn');
        if (dockBtn) {
            dockBtn.addEventListener('click', () => {
                this.buildDock();
            });
        }
        
        const treatBtn = document.getElementById('treatBtn');
        if (treatBtn) {
            treatBtn.addEventListener('click', () => {
                this.treatWater();
            });
        }
    }
    
    // Stock fish in the pond
    stockFish(fishType, cost) {
        if (!this.isActive) return;
        
        // Check if player has enough budget
        if (window.gameState.budget < cost) {
            if (window.logger) {
                window.logger.logDebug('Stocking', `Not enough budget to stock ${fishType}. Need $${cost}, have $${window.gameState.budget}`);
                window.logger.showAchievement('Not enough budget!', '💰');
            }
            return;
        }
        
        // Spend money and add fish
        if (window.gameState.spendMoney(cost)) {
            const fishCount = this.calculateFishCount(fishType, cost);
            window.gameState.addFish(fishType, fishCount);
            
            // Add visual fish to the scene
            if (window.graphicsEngine) {
                for (let i = 0; i < fishCount; i++) {
                    window.graphicsEngine.createFish(fishType);
                }
            }
            
            // Update button states
            this.updateButtonStates();
            
            if (window.logger) {
                window.logger.logDebug('Stocking', `Stocked ${fishCount} ${fishType} for $${cost}`);
                window.logger.showAchievement(`Added ${fishCount} ${fishType} to pond!`, '🐟');
            }
            
            // Check for ecosystem warnings
            this.checkEcosystemBalance();
        }
    }
    
    // Calculate how many fish to add based on cost and type
    calculateFishCount(fishType, cost) {
        const baseCounts = {
            minnows: 10,    // Small schooling fish
            bluegill: 5,    // Medium bait fish
            shad: 8,        // Medium schooling fish
            catfish: 2,     // Large predator
            bass: 3,        // Large predator
            pike: 1         // Large predator
        };
        
        return baseCounts[fishType] || 1;
    }
    
    // Add aquatic plants to improve ecosystem
    addPlants() {
        if (!this.isActive) return;
        
        const cost = this.improvementPrices.plants;
        
        if (window.gameState.budget < cost) {
            if (window.logger) {
                window.logger.logDebug('Stocking', `Not enough budget for plants. Need $${cost}, have $${window.gameState.budget}`);
                window.logger.showAchievement('Not enough budget!', '💰');
            }
            return;
        }
        
        if (window.gameState.spendMoney(cost)) {
            window.gameState.plants += 10;
            
            // Improve water quality
            window.gameState.oxygen += 1.0;
            window.gameState.grassHealth += 15;
            window.gameState.ph = Math.max(6.5, Math.min(7.5, window.gameState.ph + 0.2));
            
            // Add visual plants to scene
            if (window.graphicsEngine) {
                window.graphicsEngine.addPlants(10);
            }
            
            this.updateButtonStates();
            
            if (window.logger) {
                window.logger.logDebug('Stocking', `Added aquatic plants for $${cost}`);
                window.logger.showAchievement('Added aquatic plants! Water quality improved!', '🌱');
            }
        }
    }
    
    // Build a dock for easier fishing access
    buildDock() {
        if (!this.isActive) return;
        
        if (window.gameState.hasDock) {
            if (window.logger) {
                window.logger.logDebug('Stocking', 'Dock already built');
                window.logger.showAchievement('Dock already exists!', '🏗️');
            }
            return;
        }
        
        const cost = this.improvementPrices.dock;
        
        if (window.gameState.budget < cost) {
            if (window.logger) {
                window.logger.logDebug('Stocking', `Not enough budget for dock. Need $${cost}, have $${window.gameState.budget}`);
                window.logger.showAchievement('Not enough budget!', '💰');
            }
            return;
        }
        
        if (window.gameState.spendMoney(cost)) {
            window.gameState.hasDock = true;
            
            // Add visual dock to scene
            if (window.graphicsEngine) {
                window.graphicsEngine.addDock();
            }
            
            this.updateButtonStates();
            
            if (window.logger) {
                window.logger.logDebug('Stocking', `Built dock for $${cost}`);
                window.logger.showAchievement('Dock built! Easier fishing access!', '🏗️');
            }
        }
    }
    
    // Treat water to improve pH and quality
    treatWater() {
        if (!this.isActive) return;
        
        const cost = this.improvementPrices.treatment;
        
        if (window.gameState.budget < cost) {
            if (window.logger) {
                window.logger.logDebug('Stocking', `Not enough budget for water treatment. Need $${cost}, have $${window.gameState.budget}`);
                window.logger.showAchievement('Not enough budget!', '💰');
            }
            return;
        }
        
        if (window.gameState.spendMoney(cost)) {
            // Improve water quality significantly
            window.gameState.ph = 7.2; // Optimal pH
            window.gameState.oxygen += 2.0;
            window.gameState.grassHealth += 10;
            
            this.updateButtonStates();
            
            if (window.logger) {
                window.logger.logDebug('Stocking', `Applied water treatment for $${cost}`);
                window.logger.showAchievement('Water treated! Optimal conditions restored!', '⚗️');
            }
        }
    }
    
    // Update button states based on budget and current state
    updateButtonStates() {
        // Update fish stocking buttons
        document.querySelectorAll('.stock-button[data-fish]').forEach(button => {
            const cost = parseInt(button.getAttribute('data-cost'));
            button.disabled = window.gameState.budget < cost;
        });
        
        // Update improvement buttons
        const plantsBtn = document.getElementById('plantsBtn');
        if (plantsBtn) {
            plantsBtn.disabled = window.gameState.budget < this.improvementPrices.plants;
        }
        
        const dockBtn = document.getElementById('dockBtn');
        if (dockBtn) {
            dockBtn.disabled = window.gameState.budget < this.improvementPrices.dock || window.gameState.hasDock;
            if (window.gameState.hasDock) {
                dockBtn.textContent = '🏗️ Dock Built ✓';
            }
        }
        
        const treatBtn = document.getElementById('treatBtn');
        if (treatBtn) {
            treatBtn.disabled = window.gameState.budget < this.improvementPrices.treatment;
        }
    }
    
    // Check ecosystem balance and provide warnings
    checkEcosystemBalance() {
        const balance = window.gameState.calculateEcosystemBalance();
        const totalFish = Object.values(window.gameState.fishPopulation).reduce((sum, count) => sum + count, 0);
        
        // Check for overpopulation
        if (totalFish > 100) {
            if (window.logger) {
                window.logger.logDebug('Ecosystem', 'Pond overpopulation warning');
                window.logger.showAchievement('Warning: Pond overpopulation!', '⚠️');
            }
        }
        
        // Check predator/prey balance
        const predators = window.gameState.fishPopulation.bass + window.gameState.fishPopulation.catfish + window.gameState.fishPopulation.pike;
        const prey = window.gameState.fishPopulation.bluegill + window.gameState.fishPopulation.minnows + window.gameState.fishPopulation.shad;
        
        if (predators > prey && prey > 0) {
            if (window.logger) {
                window.logger.logDebug('Ecosystem', 'Too many predators warning');
                window.logger.showAchievement('Warning: Too many predators!', '🦈');
            }
        } else if (prey > predators * 10 && predators > 0) {
            if (window.logger) {
                window.logger.logDebug('Ecosystem', 'Too many prey fish warning');
                window.logger.showAchievement('Consider adding predator fish!', '🐠');
            }
        }
        
        // Check water quality
        if (window.gameState.oxygen < 6.0) {
            if (window.logger) {
                window.logger.logDebug('Ecosystem', 'Low oxygen warning');
                window.logger.showAchievement('Warning: Low oxygen levels!', '💨');
            }
        }
        
        if (window.gameState.ph < 6.5 || window.gameState.ph > 8.0) {
            if (window.logger) {
                window.logger.logDebug('Ecosystem', 'pH imbalance warning');
                window.logger.showAchievement('Warning: pH levels need adjustment!', '⚗️');
            }
        }
    }
    
    // Get stocking recommendations based on current state
    getStockingRecommendations() {
        const recommendations = [];
        const totalFish = Object.values(window.gameState.fishPopulation).reduce((sum, count) => sum + count, 0);
        
        if (totalFish === 0) {
            recommendations.push('Start with bait fish like bluegill and minnows');
            recommendations.push('Add some aquatic plants to improve water quality');
        } else {
            const predators = window.gameState.fishPopulation.bass + window.gameState.fishPopulation.catfish + window.gameState.fishPopulation.pike;
            const prey = window.gameState.fishPopulation.bluegill + window.gameState.fishPopulation.minnows + window.gameState.fishPopulation.shad;
            
            if (predators === 0 && prey > 10) {
                recommendations.push('Add predator fish like bass or catfish to control population');
            } else if (predators > prey) {
                recommendations.push('Add more bait fish to feed predators');
            }
            
            if (window.gameState.plants < 20) {
                recommendations.push('Add more aquatic plants for better ecosystem');
            }
            
            if (!window.gameState.hasDock && window.gameState.budget >= 500) {
                recommendations.push('Build a dock for easier fishing access');
            }
        }
        
        return recommendations;
    }
    
    // Activate stocking system
    activate() {
        this.isActive = true;
        this.updateButtonStates();
        
        if (window.logger) {
            window.logger.logDebug('Stocking', 'Stocking system activated');
        }
    }
    
    // Deactivate stocking system
    deactivate() {
        this.isActive = false;
        
        if (window.logger) {
            window.logger.logDebug('Stocking', 'Stocking system deactivated');
        }
    }
    
    // Update stocking system
    update() {
        if (!this.isActive) return;
        
        // Periodically update button states
        this.updateButtonStates();
    }
}

// Initialize stocking system
window.stockingSystem = new StockingSystem();
