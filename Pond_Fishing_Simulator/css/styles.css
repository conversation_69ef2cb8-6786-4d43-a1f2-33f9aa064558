/* Texas Pond Fishing Simulator - Main Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    color: #ecf0f1;
}

#gameContainer {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#gameCanvas {
    display: block;
    width: 100%;
    height: 100%;
}

/* Loading Screen */
#loadingScreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    font-size: 18px;
}

.spinner {
    border: 4px solid #34495e;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-progress {
    margin-top: 20px;
    text-align: center;
}

.progress-bar {
    width: 300px;
    height: 20px;
    background: #34495e;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
    border: 2px solid #2c3e50;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 14px;
    color: #bdc3c7;
}

/* Buttons */
.primary-button {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    border: 2px solid transparent;
}

.primary-button:hover {
    background: linear-gradient(135deg, #2980b9, #1f618d);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.primary-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
}

.primary-button:disabled {
    background: #7f8c8d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.action-button {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    width: 100%;
    margin-bottom: 15px;
}

.action-button:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(231, 76, 60, 0.3);
}

.action-button:disabled {
    background: #7f8c8d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.stock-button {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    border: none;
    padding: 12px 16px;
    margin: 4px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120px;
    box-shadow: 0 3px 10px rgba(39, 174, 96, 0.3);
}

.stock-button:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
}

.stock-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.stock-button:disabled {
    background: #7f8c8d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.fish-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.fish-name {
    font-weight: bold;
    margin-bottom: 3px;
    text-align: center;
}

.fish-cost {
    font-size: 12px;
    opacity: 0.9;
}

/* Game Panels */
.game-panel {
    position: absolute;
    background: rgba(44, 62, 80, 0.95);
    color: #ecf0f1;
    padding: 20px;
    border-radius: 12px;
    border: 2px solid #34495e;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

#stockingPanel {
    bottom: 20px;
    left: 20px;
    display: block;
}

#fishingPanel {
    bottom: 20px;
    left: 20px;
    display: none;
}

.stock-category {
    margin-bottom: 20px;
}

.stock-category h4 {
    color: #3498db;
    margin-bottom: 10px;
    font-size: 16px;
    border-bottom: 2px solid #34495e;
    padding-bottom: 5px;
}

.stock-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

/* Fishing Controls */
.fishing-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.power-control, .tension-control {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.power-control label, .tension-control label {
    font-weight: bold;
    color: #3498db;
}

.power-bar, #tensionBar {
    width: 100%;
    height: 25px;
    background: #34495e;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    border: 2px solid #2c3e50;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3);
}

.power-fill, #tensionFill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #f39c12, #e74c3c);
    width: 50%;
    transition: width 0.2s ease;
    border-radius: 10px;
}

.fishing-info {
    background: rgba(52, 73, 94, 0.5);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #34495e;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-row:last-child {
    margin-bottom: 0;
}

/* Position specific panels */
#modeToggle {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 100;
}

#instructions {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(44, 62, 80, 0.95);
    color: #ecf0f1;
    padding: 15px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 100;
    border: 2px solid #34495e;
    max-width: 300px;
    backdrop-filter: blur(10px);
}

#instructions h4 {
    color: #3498db;
    margin-bottom: 10px;
    border-bottom: 1px solid #34495e;
    padding-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .game-panel {
        max-width: 400px;
    }
    
    .stock-buttons {
        justify-content: center;
    }
    
    .stock-button {
        min-width: 100px;
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .game-panel {
        max-width: 90vw;
        left: 5vw !important;
        right: 5vw !important;
    }
    
    #instructions {
        max-width: 90vw;
        right: 5vw !important;
    }
    
    .stock-button {
        min-width: 80px;
        font-size: 11px;
        padding: 8px 12px;
    }
    
    .fish-icon {
        font-size: 20px;
    }
}

/* Scrollbar Styling */
.game-panel::-webkit-scrollbar {
    width: 8px;
}

.game-panel::-webkit-scrollbar-track {
    background: #34495e;
    border-radius: 4px;
}

.game-panel::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 4px;
}

.game-panel::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}
