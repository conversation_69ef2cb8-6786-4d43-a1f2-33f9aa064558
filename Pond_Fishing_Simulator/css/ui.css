/* UI Components - Texas Pond Fishing Simulator */

/* Main UI Panel */
#ui {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(44, 62, 80, 0.95);
    color: #ecf0f1;
    padding: 20px;
    border-radius: 12px;
    font-size: 14px;
    z-index: 100;
    min-width: 280px;
    border: 2px solid #34495e;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    max-height: 80vh;
    overflow-y: auto;
}

.ui-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #34495e;
}

.ui-header h2 {
    color: #3498db;
    font-size: 18px;
    margin: 0;
}

.version {
    background: #27ae60;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.ui-section {
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(52, 73, 94, 0.3);
    border-radius: 8px;
    border: 1px solid #34495e;
}

.ui-section h3 {
    color: #3498db;
    font-size: 16px;
    margin-bottom: 10px;
    border-bottom: 1px solid #34495e;
    padding-bottom: 5px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 5px 0;
}

.stat-row:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-weight: 500;
    color: #bdc3c7;
}

.stat-value {
    font-weight: bold;
    color: #ecf0f1;
    background: rgba(52, 152, 219, 0.2);
    padding: 2px 8px;
    border-radius: 4px;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

/* Weather Panel */
#weather {
    position: absolute;
    top: 20px;
    right: 200px;
    background: rgba(44, 62, 80, 0.95);
    color: #ecf0f1;
    padding: 15px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 100;
    border: 2px solid #34495e;
    backdrop-filter: blur(10px);
    min-width: 200px;
}

#weather h3 {
    color: #f39c12;
    margin-bottom: 10px;
    font-size: 16px;
    border-bottom: 1px solid #34495e;
    padding-bottom: 5px;
}

.weather-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 13px;
}

.weather-row:last-child {
    margin-bottom: 0;
}

/* Debug Panel */
#debugPanel {
    position: absolute;
    top: 20px;
    right: 420px;
    background: rgba(0, 0, 0, 0.9);
    color: #00ff00;
    padding: 15px;
    border-radius: 8px;
    font-size: 11px;
    z-index: 100;
    max-width: 350px;
    max-height: 300px;
    overflow-y: auto;
    border: 2px solid #27ae60;
    font-family: 'Courier New', monospace;
    backdrop-filter: blur(5px);
}

#debugPanel h4 {
    color: #27ae60;
    margin-bottom: 10px;
    font-size: 14px;
    border-bottom: 1px solid #27ae60;
    padding-bottom: 5px;
}

#debugLog {
    max-height: 200px;
    overflow-y: auto;
}

.debug-item {
    margin: 2px 0;
    padding: 2px 5px;
    border-left: 2px solid #27ae60;
    background: rgba(39, 174, 96, 0.1);
    border-radius: 3px;
    font-size: 10px;
    line-height: 1.3;
}

/* Error Log */
#errorLog {
    position: absolute;
    bottom: 20px;
    right: 350px;
    background: rgba(0, 0, 0, 0.9);
    color: #ff6b6b;
    padding: 15px;
    border-radius: 8px;
    font-size: 11px;
    z-index: 100;
    max-width: 350px;
    max-height: 200px;
    overflow-y: auto;
    border: 2px solid #e74c3c;
    display: none;
    font-family: 'Courier New', monospace;
    backdrop-filter: blur(5px);
}

#errorLog h4 {
    color: #e74c3c;
    margin-bottom: 10px;
    font-size: 14px;
    border-bottom: 1px solid #e74c3c;
    padding-bottom: 5px;
}

.error-item {
    margin: 2px 0;
    padding: 3px 5px;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 3px;
    border-left: 2px solid #e74c3c;
    font-size: 10px;
    line-height: 1.3;
}

/* Achievement Notifications */
#achievementNotification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 20px 30px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: bold;
    z-index: 1000;
    box-shadow: 0 10px 30px rgba(243, 156, 18, 0.5);
    border: 3px solid #d68910;
    display: none;
    animation: achievementPop 0.5s ease-out;
}

@keyframes achievementPop {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.achievement-icon {
    font-size: 32px;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.achievement-text {
    font-size: 16px;
}

/* Responsive UI Adjustments */
@media (max-width: 1400px) {
    #debugPanel {
        right: 20px;
        top: 120px;
        max-width: 300px;
    }
    
    #weather {
        right: 20px;
        top: 440px;
    }
    
    #errorLog {
        right: 20px;
        bottom: 200px;
        max-width: 300px;
    }
}

@media (max-width: 1024px) {
    #ui {
        min-width: 250px;
        font-size: 13px;
    }
    
    #debugPanel, #errorLog {
        display: none !important;
    }
    
    #weather {
        min-width: 180px;
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    #ui {
        min-width: 200px;
        font-size: 12px;
        padding: 15px;
        top: 10px;
        left: 10px;
    }
    
    .ui-header h2 {
        font-size: 16px;
    }
    
    .stat-row {
        font-size: 12px;
    }
    
    #weather {
        min-width: 160px;
        font-size: 11px;
        padding: 10px;
        top: 10px;
        right: 10px;
    }
    
    #modeToggle {
        top: 10px;
        right: 10px;
        font-size: 14px;
        padding: 10px 16px;
    }
}

/* Scrollbar Styling for UI panels */
#ui::-webkit-scrollbar,
#debugLog::-webkit-scrollbar,
#errorLog::-webkit-scrollbar {
    width: 6px;
}

#ui::-webkit-scrollbar-track,
#debugLog::-webkit-scrollbar-track,
#errorLog::-webkit-scrollbar-track {
    background: rgba(52, 73, 94, 0.5);
    border-radius: 3px;
}

#ui::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 3px;
}

#debugLog::-webkit-scrollbar-thumb {
    background: #27ae60;
    border-radius: 3px;
}

#errorLog::-webkit-scrollbar-thumb {
    background: #e74c3c;
    border-radius: 3px;
}

#ui::-webkit-scrollbar-thumb:hover,
#debugLog::-webkit-scrollbar-thumb:hover,
#errorLog::-webkit-scrollbar-thumb:hover {
    opacity: 0.8;
}
